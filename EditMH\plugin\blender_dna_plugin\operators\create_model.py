"""
Create Model Operator for the Blender MetaHuman DNA Plugin.
"""

import os
import sys
import bpy
import math
import traceback
from bpy.props import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roperty, EnumProperty, IntProperty
from mathutils import Vector, Matrix
import mathutils

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, log_dna_info
from ..utils.ui_utils import update_ui
from ..utils.rotation_utils import dna_to_blender_coords, apply_dna_to_blender_rotation
from ..utils.material_utils import UV_MAP_NAME

# Ensure the DNA modules are in the Python path
def ensure_dna_modules_path():
    """Ensure the DNA modules are in the Python path"""
    # Get the addon path
    addon_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))
    dnacalib_path = os.path.join(addon_path, "dnacalib", "py3.11")

    # Check if the path exists
    if not os.path.exists(dnacalib_path):
        print(f"DNA calibration path does not exist: {dnacalib_path}")
        return False

    # Add the path to sys.path if it's not already there
    if dnacalib_path not in sys.path:
        print(f"Adding DNA calibration path to sys.path: {dnacalib_path}")
        sys.path.append(dnacalib_path)

    return True

class DNA_OT_CreateModel(bpy.types.Operator):
    """Create a 3D model from the imported DNA file"""
    bl_idname = "dna.create_model"
    bl_label = "Create Model from DNA"
    bl_description = "Create a 3D model from the imported DNA file"
    bl_options = {'REGISTER', 'UNDO'}

    # Properties for LOD selection using checkboxes
    create_lod0: BoolProperty(
        name="LOD 0 (High)",
        description="Create the highest level of detail",
        default=True
    )

    create_lod1: BoolProperty(
        name="LOD 1",
        description="Create medium-high level of detail",
        default=False
    )

    create_lod2: BoolProperty(
        name="LOD 2",
        description="Create medium level of detail",
        default=False
    )

    create_lod3: BoolProperty(
        name="LOD 3",
        description="Create medium-low level of detail",
        default=False
    )

    create_lod4: BoolProperty(
        name="LOD 4",
        description="Create low level of detail",
        default=False
    )

    # Armature is no longer created by default
    create_armature: BoolProperty(
        name="Create Armature",
        description="Create an armature with the joints from the DNA file",
        default=False,
        options={'HIDDEN'}  # Hide this property as we now create the armature separately
    )

    def execute(self, context):
        """Execute the create model operation"""
        print("\n=== DNA Create Model Process Started ===")

        # Get the selected LOD levels from window manager properties
        create_lod0 = context.window_manager.create_lod0
        create_lod1 = context.window_manager.create_lod1
        create_lod2 = context.window_manager.create_lod2
        create_lod3 = context.window_manager.create_lod3
        create_lod4 = context.window_manager.create_lod4
        create_lod5 = context.window_manager.create_lod5
        create_lod6 = context.window_manager.create_lod6
        create_lod7 = context.window_manager.create_lod7

        # Check if at least one LOD is selected
        if not any([create_lod0, create_lod1, create_lod2, create_lod3, create_lod4, create_lod5, create_lod6, create_lod7]):
            self.report({'ERROR'}, "Please select at least one LOD level to create")
            return {'CANCELLED'}

        # Update status message
        context.scene.dna_tools.status_message = "Creating model..."
        update_ui()

        # Ensure the DNA modules are in the Python path
        print(f"Ensuring DNA modules path...")
        ensure_dna_modules_path()

        print(f"Checking DNA modules availability...")
        # Check if DNA modules are available
        dna_available = check_dna_modules()
        print(f"DNA modules available: {dna_available}")

        if not dna_available:
            print(f"DNA modules not available, cancelling model creation")
            context.scene.dna_tools.status_message = "Error: DNA modules not available"
            update_ui()
            self.report({'ERROR'}, "DNA modules not available. Check the installation.")
            return {'CANCELLED'}

        print(f"DNA modules check passed, proceeding with model creation")

        # Check if a DNA file is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            print(f"No DNA file loaded, cancelling model creation")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Create the model
        try:
            print(f"Creating model from DNA file...")
            context.scene.dna_tools.status_message = "Creating model from DNA file..."
            update_ui()

            self.create_model_from_dna(context)

            # Set the flag to indicate mesh has been created
            context.scene.dna_tools.is_mesh_created = True

            print(f"Model creation completed successfully")
            context.scene.dna_tools.status_message = "Model creation completed successfully"
            update_ui()

            return {'FINISHED'}
        except Exception as e:
            print(f"Error during model creation: {str(e)}")
            traceback.print_exc()
            context.scene.dna_tools.status_message = f"Error during model creation: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error creating model: {str(e)}")
            return {'CANCELLED'}

    def create_model_from_dna(self, context):
        """Create a 3D model from the DNA file"""
        # Import the DNA modules
        import dna
        from dna import FileStream, BinaryStreamReader, Status, DataLayer_All

        # Get the DNA file path
        dna_file_path = context.scene.dna_tools.dna_file_path
        print(f"Creating model from DNA file: {dna_file_path}")

        # Get the selected LOD levels from window manager properties
        create_lod0 = context.window_manager.create_lod0
        create_lod1 = context.window_manager.create_lod1
        create_lod2 = context.window_manager.create_lod2
        create_lod3 = context.window_manager.create_lod3
        create_lod4 = context.window_manager.create_lod4
        create_lod5 = context.window_manager.create_lod5
        create_lod6 = context.window_manager.create_lod6
        create_lod7 = context.window_manager.create_lod7

        # Create a list of LODs to process
        lods_to_create = []
        if create_lod0:
            lods_to_create.append(0)
        if create_lod1:
            lods_to_create.append(1)
        if create_lod2:
            lods_to_create.append(2)
        if create_lod3:
            lods_to_create.append(3)
        if create_lod4:
            lods_to_create.append(4)
        if create_lod5:
            lods_to_create.append(5)
        if create_lod6:
            lods_to_create.append(6)
        if create_lod7:
            lods_to_create.append(7)

        print(f"Creating LOD levels: {lods_to_create}")

        # Create a parent collection for all LODs
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
        parent_collection = bpy.data.collections.new(f"{model_name}")
        bpy.context.scene.collection.children.link(parent_collection)

        # Create armature once for all LODs
        armature_obj = None

        # Process each selected LOD
        for lod in lods_to_create:
            print(f"\nProcessing LOD level: {lod}")

            # Create a file stream for each LOD
            stream = FileStream(dna_file_path, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)

            # Create the reader with the current LOD
            reader = BinaryStreamReader.create(stream, DataLayer_All, lod)

            # Read the DNA file
            reader.read()

            # Check for errors
            if not Status.isOk():
                status = Status.get()
                error_msg = f"Error loading DNA for LOD {lod}: {status.message}"
                print(f"Error detected: {error_msg}")
                continue  # Skip this LOD and try the next one

            # Try to get LOD information safely
            try:
                lod_count = reader.getLODCount()
                print(f"DNA file contains {lod_count} LOD levels")
            except Exception as e:
                print(f"Could not get LOD count: {e}")

            # Create a collection for this LOD
            lod_collection = bpy.data.collections.new(f"{model_name}_LOD{lod}")
            parent_collection.children.link(lod_collection)

            # We no longer create the armature here
            # It will be created separately with the Create Armature button

            # Store the current LOD level for mesh creation
            self.lod_level = str(lod)

            # Create the meshes for this LOD
            self.create_meshes_from_dna(context, reader, lod_collection, armature_obj)

        # Select the parent collection
        for obj in parent_collection.objects:
            obj.select_set(True)
            context.view_layer.objects.active = obj
            break

        # If we created an armature, select it
        if armature_obj:
            for obj in bpy.context.selected_objects:
                obj.select_set(False)
            armature_obj.select_set(True)
            context.view_layer.objects.active = armature_obj

    def create_armature_from_dna(self, context, reader, collection):
        """Create an armature from the DNA file"""
        print(f"Creating armature...")
        context.scene.dna_tools.status_message = "Creating armature..."
        update_ui()

        # Create the armature
        armature = bpy.data.armatures.new("MetaHuman_Armature")
        armature_obj = bpy.data.objects.new("MetaHuman_Armature", armature)
        collection.objects.link(armature_obj)

        # Enter edit mode to create bones
        context.view_layer.objects.active = armature_obj
        bpy.ops.object.mode_set(mode='EDIT')

        # Create the bones
        joint_count = reader.getJointCount()
        print(f"Creating {joint_count} joints...")

        # Create a dictionary to store the edit bones
        edit_bones = {}

        # First pass: create all bones
        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)
            edit_bone = armature.edit_bones.new(joint_name)
            edit_bones[joint_index] = edit_bone

            # Set the bone position using neutral joint translations
            x = reader.getNeutralJointTranslationXs()[joint_index]
            y = reader.getNeutralJointTranslationYs()[joint_index]
            z = reader.getNeutralJointTranslationZs()[joint_index]
            edit_bone.head = Vector((x, z, y))  # Swap Y and Z for Blender

            # Set a default tail position (will be adjusted later)
            edit_bone.tail = Vector((x, z, y + 0.05))

        # Second pass: set parent-child relationships and adjust tails
        for joint_index in range(joint_count):
            parent_index = reader.getJointParentIndex(joint_index)

            if parent_index != joint_index and parent_index < joint_count:
                # Set the parent
                edit_bones[joint_index].parent = edit_bones[parent_index]

                # Adjust the parent's tail to point to this bone
                parent_bone = edit_bones[parent_index]
                child_bone = edit_bones[joint_index]

                # Only adjust if the bones are close enough
                distance = (parent_bone.head - child_bone.head).length
                if distance < 0.5:  # Threshold to avoid connecting distant bones
                    parent_bone.tail = child_bone.head

        # Exit edit mode
        bpy.ops.object.mode_set(mode='OBJECT')

        print(f"Armature created successfully")
        return armature_obj

    def create_meshes_from_dna(self, context, reader, collection, armature_obj=None):
        """Create meshes from the DNA file"""
        print(f"Creating meshes...")
        context.scene.dna_tools.status_message = "Creating meshes..."
        update_ui()

        # Get the number of meshes
        mesh_count = reader.getMeshCount()
        print(f"Creating {mesh_count} meshes...")

        # Log all mesh names for debugging
        print(f"Available meshes in DNA file at LOD {self.lod_level}:")
        mesh_names = []
        try:
            for i in range(mesh_count):
                mesh_name = reader.getMeshName(i)
                mesh_names.append(mesh_name)
                print(f"  Mesh {i}: {mesh_name}")
        except Exception as e:
            print(f"Error listing meshes: {e}")

        # Selected LOD level
        selected_lod = int(self.lod_level)

        # Define LOD patterns to check
        lod_patterns = [
            f"_lod{selected_lod}_",
            f"_LOD{selected_lod}_",
            f"_lod{selected_lod}.",
            f"_LOD{selected_lod}.",
            f".lod{selected_lod}.",
            f".LOD{selected_lod}.",
            f"_lod{selected_lod}",
            f"_LOD{selected_lod}",
            f"lod{selected_lod}_",
            f"LOD{selected_lod}_",
            f"lod{selected_lod}.",
            f"LOD{selected_lod}.",
            f"lod{selected_lod}",
            f"LOD{selected_lod}",
            f"_l{selected_lod}_",  # Some files use l0, l1, etc.
            f"_L{selected_lod}_",
            f"_l{selected_lod}.",
            f"_L{selected_lod}."
        ]

        # Special case for LOD 0: base meshes often don't have LOD suffix
        if selected_lod == 0:
            # For LOD 0, also include meshes that don't have any LOD suffix
            # First, identify meshes that have LOD suffixes for any level
            any_lod_pattern = r'(_|\.)(?:lod|LOD|l|L)[0-9]+(?:_|\.)'
            import re
            meshes_with_lod = [i for i, name in enumerate(mesh_names) if re.search(any_lod_pattern, name)]

            # Meshes without LOD suffix are candidates for LOD 0
            lod0_candidates = [i for i in range(len(mesh_names)) if i not in meshes_with_lod]

            # Meshes that explicitly match LOD 0 patterns
            lod0_explicit = [i for i, name in enumerate(mesh_names) if any(pattern in name for pattern in lod_patterns)]

            # Combine both sets
            lod_meshes = list(set(lod0_candidates + lod0_explicit))

            print(f"LOD 0 special case: Found {len(lod0_explicit)} explicit LOD 0 meshes and {len(lod0_candidates)} candidate meshes without LOD suffix.")
        else:
            # For other LODs, just use the pattern matching
            lod_meshes = [i for i, name in enumerate(mesh_names) if any(pattern in name for pattern in lod_patterns)]

        # If no meshes match the LOD patterns, use all meshes
        use_lod_filtering = True
        if not lod_meshes:
            print(f"Warning: No meshes found matching LOD {selected_lod} patterns. Using all meshes.")
            use_lod_filtering = False
        else:
            print(f"Found {len(lod_meshes)} meshes matching LOD {selected_lod} patterns.")

        # Create each mesh
        for mesh_index in range(mesh_count):
            mesh_name = reader.getMeshName(mesh_index)

            # Filter meshes by LOD level if filtering is enabled
            if use_lod_filtering and mesh_index not in lod_meshes:
                print(f"Skipping mesh {mesh_name} (doesn't match LOD {selected_lod})")
                continue

            print(f"Creating mesh: {mesh_name}")

            # Create a new mesh
            mesh = bpy.data.meshes.new(mesh_name)
            mesh_obj = bpy.data.objects.new(mesh_name, mesh)
            collection.objects.link(mesh_obj)

            # Initialize UV maps following example plugin approach
            self.init_uvs(mesh)

            # Get the vertices
            vertex_count = reader.getVertexPositionCount(mesh_index)
            vertices = []

            for vertex_index in range(vertex_count):
                position = reader.getVertexPosition(mesh_index, vertex_index)
                # Convert DNA coordinates to Blender coordinates using our utility function
                x, y, z = dna_to_blender_coords(position[0], position[1], position[2])

                # Add vertex with original coordinates (we'll rotate the entire mesh later)
                vertices.append((x, y, z))

            # Get the faces
            face_count = reader.getFaceCount(mesh_index)
            faces = []

            for face_index in range(face_count):
                # Get the vertex layout indices for this face
                layout_indices = list(reader.getFaceVertexLayoutIndices(mesh_index, face_index))

                # Get the position indices for these layout indices
                position_indices = []

                # Get all vertex layout position indices for this mesh
                vertex_layout_position_indices = reader.getVertexLayoutPositionIndices(mesh_index)

                for layout_idx in layout_indices:
                    position_idx = vertex_layout_position_indices[layout_idx]
                    position_indices.append(position_idx)

                faces.append(position_indices)

            # Check if UVs are available
            has_uvs = reader.getVertexTextureCoordinateCount(mesh_index) > 0

            # Create the mesh
            mesh.from_pydata(vertices, [], faces)
            mesh.update()

            # Enable smooth shading for all polygons
            for poly in mesh.polygons:
                poly.use_smooth = True

            # Check if normals are available
            normal_count = reader.getVertexNormalCount(mesh_index)
            print(f"Normal count for mesh {mesh_name}: {normal_count}")

            if normal_count > 0:
                print(f"Adding normals for mesh {mesh_name}")
                try:
                    # Get the normal arrays
                    normal_xs = reader.getVertexNormalXs(mesh_index)
                    normal_ys = reader.getVertexNormalYs(mesh_index)
                    normal_zs = reader.getVertexNormalZs(mesh_index)

                    # Get the layout indices for normals
                    normal_indices = reader.getVertexLayoutNormalIndices(mesh_index)

                    # Enable auto smooth - in Blender 4.4+ this is on the object, not the mesh
                    try:
                        # First try the new location (Blender 4.4+)
                        mesh_obj.use_auto_smooth = True
                        # Set auto smooth angle to 180 degrees (PI radians) to ensure all edges are smooth
                        mesh_obj.auto_smooth_angle = math.pi
                    except AttributeError:
                        try:
                            # Fall back to the old location (Blender 2.8-4.3)
                            mesh.use_auto_smooth = True
                            # Set auto smooth angle to 180 degrees (PI radians) to ensure all edges are smooth
                            mesh.auto_smooth_angle = math.pi
                        except AttributeError:
                            print(f"Warning: Could not enable auto smooth for mesh {mesh_name} - attribute not found on mesh or object")

                    # Create a list of normals for each vertex
                    vertex_normals = []
                    for index in range(len(vertices)):
                        if index < len(normal_indices) and normal_indices[index] < len(normal_xs):
                            # Get normal index for this vertex
                            normal_idx = normal_indices[index]

                            # Get the normal coordinates without coordinate system conversion
                            # Keep original DNA coordinates (no swapping)
                            nx = normal_xs[normal_idx]
                            ny = normal_ys[normal_idx]
                            nz = normal_zs[normal_idx]

                            # Add the normal to the list
                            vertex_normals.append(mathutils.Vector((nx, ny, nz)).normalized())
                        else:
                            # Use a default normal if the index is out of range
                            vertex_normals.append(mathutils.Vector((0, 0, 1)))

                    # Set custom normals from vertices
                    mesh.use_auto_smooth = True
                    mesh.normals_split_custom_set_from_vertices(vertex_normals)
                    print(f"Assigned {len(vertex_normals)} normals for mesh {mesh_name}")

                    # Make sure normals are properly calculated
                    bpy.ops.object.select_all(action='DESELECT')
                    mesh_obj.select_set(True)
                    bpy.context.view_layer.objects.active = mesh_obj

                    # Set to edit mode to recalculate normals
                    bpy.ops.object.mode_set(mode='EDIT')
                    bpy.ops.mesh.select_all(action='SELECT')
                    bpy.ops.mesh.normals_make_consistent(inside=False)
                    bpy.ops.object.mode_set(mode='OBJECT')

                except Exception as e:
                    print(f"Error assigning normals for mesh {mesh_name}: {e}")
                    import traceback
                    traceback.print_exc()

                    # Fallback: recalculate normals if custom normal assignment fails
                    try:
                        print(f"Attempting fallback normal calculation for mesh {mesh_name}")
                        bpy.ops.object.select_all(action='DESELECT')
                        mesh_obj.select_set(True)
                        bpy.context.view_layer.objects.active = mesh_obj

                        # Set to edit mode to recalculate normals
                        bpy.ops.object.mode_set(mode='EDIT')
                        bpy.ops.mesh.select_all(action='SELECT')
                        bpy.ops.mesh.normals_make_consistent(inside=False)
                        bpy.ops.object.mode_set(mode='OBJECT')
                        print(f"Fallback normal calculation completed for mesh {mesh_name}")
                    except Exception as e2:
                        print(f"Error in fallback normal calculation for mesh {mesh_name}: {e2}")
                        import traceback
                        traceback.print_exc()

            # Add UVs if available using the proper layout indexing
            if has_uvs:
                print(f"Adding UVs for mesh {mesh_name}")
                try:
                    # Get the UV coordinate arrays
                    tex_coord_us = reader.getVertexTextureCoordinateUs(mesh_index)
                    tex_coord_vs = reader.getVertexTextureCoordinateVs(mesh_index)

                    # Get the layout indices for texture coordinates
                    vertex_layout_uv_indices = reader.getVertexLayoutTextureCoordinateIndices(mesh_index)

                    # Get the active UV layer (already initialized with correct name)
                    uv_layer = mesh.uv_layers.active

                    # Create a mapping from face index in DNA to face index in Blender
                    # This is more reliable than trying to match faces by vertices
                    face_mapping = {}

                    # Get the total number of UVs and faces for logging
                    uv_count = reader.getVertexTextureCoordinateCount(mesh_index)
                    face_count = reader.getFaceCount(mesh_index)
                    print(f"Mesh {mesh_name} has {uv_count} UVs and {face_count} faces")
                except Exception as e:
                    print(f"Error initializing UV data for mesh {mesh_name}: {e}")
                    continue

                # First, create a dictionary mapping vertex indices to Blender faces
                try:
                    vertex_to_face = {}
                    for poly_idx, poly in enumerate(mesh.polygons):
                        # Create a tuple of sorted vertex indices as a key
                        key = tuple(sorted([v for v in poly.vertices]))
                        vertex_to_face[key] = poly_idx

                    # Now map each DNA face to a Blender face
                    for face_index in range(reader.getFaceCount(mesh_index)):
                        try:
                            # Get the layout indices for this face
                            face_layout_indices = list(reader.getFaceVertexLayoutIndices(mesh_index, face_index))

                            # Get the position indices for this face
                            vertex_layout_position_indices = reader.getVertexLayoutPositionIndices(mesh_index)
                            face_position_indices = [vertex_layout_position_indices[layout_idx] for layout_idx in face_layout_indices]

                            # Create a key for this face
                            key = tuple(sorted(face_position_indices))

                            # If we find a matching Blender face, add it to our mapping
                            if key in vertex_to_face:
                                face_mapping[face_index] = vertex_to_face[key]
                        except Exception as e:
                            print(f"Error mapping DNA face {face_index} to Blender face: {e}")
                except Exception as e:
                    print(f"Error creating face mapping for mesh {mesh_name}: {e}")

                print(f"Mapped {len(face_mapping)} out of {face_count} faces for UV assignment")

                # Now assign UVs using our face mapping
                uv_assignments = 0
                try:
                    for dna_face_index, blender_face_index in face_mapping.items():
                        try:
                            # Get the layout indices for this DNA face
                            face_layout_indices = list(reader.getFaceVertexLayoutIndices(mesh_index, dna_face_index))

                            # Get the Blender face
                            blender_face = mesh.polygons[blender_face_index]

                            # We need to match the vertices in the DNA face to the vertices in the Blender face
                            # to ensure we assign UVs to the correct loops
                            vertex_layout_position_indices = reader.getVertexLayoutPositionIndices(mesh_index)
                            dna_face_vertices = [vertex_layout_position_indices[layout_idx] for layout_idx in face_layout_indices]

                            # Create a mapping from DNA vertex index to layout index
                            dna_vertex_to_layout = {}
                            for i, layout_idx in enumerate(face_layout_indices):
                                vertex_idx = vertex_layout_position_indices[layout_idx]
                                dna_vertex_to_layout[vertex_idx] = layout_idx

                            # For each loop in the Blender face
                            for loop_idx in blender_face.loop_indices:
                                try:
                                    # Get the vertex index for this loop
                                    vertex_idx = mesh.loops[loop_idx].vertex_index

                                    # Find the corresponding layout index in the DNA face
                                    if vertex_idx in dna_vertex_to_layout:
                                        layout_idx = dna_vertex_to_layout[vertex_idx]

                                        # Check if the layout index is valid
                                        if layout_idx < len(vertex_layout_uv_indices):
                                            # Get the UV index for this layout
                                            uv_idx = vertex_layout_uv_indices[layout_idx]
                                        else:
                                            print(f"Warning: Layout index {layout_idx} out of range (max: {len(vertex_layout_uv_indices)-1})")
                                            continue

                                        # Check if the UV index is valid
                                        if uv_idx < len(tex_coord_us) and uv_idx < len(tex_coord_vs):
                                            # Get the UV coordinates
                                            u = tex_coord_us[uv_idx]
                                            v = tex_coord_vs[uv_idx]

                                            # Flip V coordinate for Blender
                                            uv_layer.data[loop_idx].uv = (u, 1.0 - v)
                                            uv_assignments += 1
                                        else:
                                            print(f"Warning: UV index {uv_idx} out of range (max U: {len(tex_coord_us)-1}, max V: {len(tex_coord_vs)-1})")
                                except Exception as e:
                                    print(f"Error assigning UV for loop {loop_idx} in face {blender_face_index}: {e}")
                        except Exception as e:
                            print(f"Error processing DNA face {dna_face_index} to Blender face {blender_face_index}: {e}")
                except Exception as e:
                    print(f"Error assigning UVs for mesh {mesh_name}: {e}")

                print(f"Assigned {uv_assignments} UV coordinates for mesh {mesh_name}")

                # If we didn't assign many UVs, try a fallback method
                if uv_assignments < len(mesh.loops) * 0.5:  # If less than 50% of loops got UVs
                    print(f"UV assignment may be incomplete. Trying fallback method...")
                    try:
                        # Clear the existing UV layer
                        mesh.uv_layers.remove(uv_layer)

                        # Re-initialize UV layer with correct name following example plugin
                        self.init_uvs(mesh)
                        uv_layer = mesh.uv_layers.active

                        try:
                            # Try a direct approach using the vertex layout indices
                            # This is less accurate but might work better in some cases
                            vertex_layout_position_indices = reader.getVertexLayoutPositionIndices(mesh_index)

                            # Create a mapping from position index to UV index
                            position_to_uv = {}

                            # Check if the arrays have the same length
                            if len(vertex_layout_position_indices) == len(vertex_layout_uv_indices):
                                for layout_idx in range(len(vertex_layout_position_indices)):
                                    try:
                                        position_idx = vertex_layout_position_indices[layout_idx]
                                        uv_idx = vertex_layout_uv_indices[layout_idx]
                                        position_to_uv[position_idx] = uv_idx
                                    except Exception as e:
                                        print(f"Error mapping position to UV at layout index {layout_idx}: {e}")
                            else:
                                print(f"Warning: Position indices ({len(vertex_layout_position_indices)}) and UV indices ({len(vertex_layout_uv_indices)}) arrays have different lengths")

                                # Try to use face layout indices to map positions to UVs
                                for face_index in range(reader.getFaceCount(mesh_index)):
                                    try:
                                        face_layout_indices = list(reader.getFaceVertexLayoutIndices(mesh_index, face_index))

                                        for layout_idx in face_layout_indices:
                                            try:
                                                # Check if layout index is valid for both arrays
                                                if layout_idx < len(vertex_layout_position_indices) and layout_idx < len(vertex_layout_uv_indices):
                                                    position_idx = vertex_layout_position_indices[layout_idx]
                                                    uv_idx = vertex_layout_uv_indices[layout_idx]

                                                    # Check if UV index is valid
                                                    if uv_idx < len(tex_coord_us) and uv_idx < len(tex_coord_vs):
                                                        position_to_uv[position_idx] = uv_idx
                                            except Exception as e:
                                                print(f"Error processing layout index {layout_idx} for face {face_index}: {e}")
                                    except Exception as e:
                                        print(f"Error getting face layout indices for face {face_index}: {e}")

                            # Assign UVs to all loops
                            fallback_assignments = 0
                            for face in mesh.polygons:
                                for loop_idx in face.loop_indices:
                                    try:
                                        loop = mesh.loops[loop_idx]
                                        vertex_idx = loop.vertex_index

                                        if vertex_idx in position_to_uv:
                                            uv_idx = position_to_uv[vertex_idx]

                                            # Check if the UV index is valid
                                            if uv_idx < len(tex_coord_us) and uv_idx < len(tex_coord_vs):
                                                u = tex_coord_us[uv_idx]
                                                v = tex_coord_vs[uv_idx]
                                                uv_layer.data[loop_idx].uv = (u, 1.0 - v)
                                                fallback_assignments += 1
                                            else:
                                                print(f"Warning: UV index {uv_idx} out of range (max U: {len(tex_coord_us)-1}, max V: {len(tex_coord_vs)-1})")
                                    except Exception as e:
                                        print(f"Error assigning fallback UV for loop {loop_idx}: {e}")
                        except Exception as e:
                            print(f"Error during fallback UV mapping setup: {e}")

                        print(f"Fallback method assigned {fallback_assignments} UV coordinates for mesh {mesh_name}")
                    except Exception as e:
                        print(f"Error during fallback UV assignment: {e}")

            # Apply a 90-degree X-axis rotation to the mesh using our utility function
            # This matches the approach in the example implementation
            print(f"Applying 90-degree X-axis rotation to mesh {mesh_name}")
            apply_dna_to_blender_rotation(mesh_obj)

            # Apply the rotation to bake it into the mesh data
            bpy.ops.object.select_all(action='DESELECT')
            mesh_obj.select_set(True)
            bpy.context.view_layer.objects.active = mesh_obj
            bpy.ops.object.transform_apply(location=False, rotation=True, scale=False)

            # Vertex groups and weights will be created and applied later with the Apply Weights button
            # No need to create empty vertex groups here

            # Create blendshapes (shape keys)
            self.create_blendshapes(mesh_obj, reader, mesh_index)

            # Materials will be implemented later
            pass

            # We no longer parent to armature here
            # This will be done when the armature is created

        print(f"Meshes created successfully")

    # The create_vertex_groups method has been removed
    # Vertex groups are now created and weights are applied in the apply_weights.py file

    def create_blendshapes(self, mesh_obj, reader, mesh_index):
        """Create blendshapes (shape keys) from DNA data

        Args:
            mesh_obj: Blender mesh object
            reader: DNA reader
            mesh_index: Index of the mesh in the DNA file
        """
        print(f"Creating blendshapes for {mesh_obj.name}")

        try:
            # Check if the mesh has blendshapes
            blendshape_target_count = reader.getBlendShapeTargetCount(mesh_index)
            if blendshape_target_count == 0:
                print(f"No blendshapes found for mesh {mesh_obj.name}")
                return

            print(f"Found {blendshape_target_count} blendshape targets")

            # Import the mesh utilities
            from ..utils.mesh_utils import initialize_shape_key_container

            # Initialize the shape key container if it doesn't exist
            if not mesh_obj.data.shape_keys:
                # Create the basis shape key and set the container name to match the mesh name
                shape_key_container = initialize_shape_key_container(mesh_obj)
                print(f"Created basis shape key and set container name to: {shape_key_container.name}")
            else:
                # If shape keys already exist, make sure the container name matches the mesh name
                mesh_obj.data.shape_keys.name = mesh_obj.name
                print(f"Updated shape key container name to: {mesh_obj.data.shape_keys.name}")

            # Process each blendshape target
            for target_index in range(blendshape_target_count):
                try:
                    # Get the channel index and name
                    channel_index = reader.getBlendShapeChannelIndex(mesh_index, target_index)
                    channel_name = reader.getBlendShapeChannelName(channel_index)

                    # Create the prefixed shape key name (meshname__shapekey)
                    prefixed_channel_name = f"{mesh_obj.name}__{channel_name}"

                    print(f"Creating blendshape: {prefixed_channel_name}")

                    # Create the shape key with the prefixed name
                    shape_key = mesh_obj.shape_key_add(name=prefixed_channel_name)
                    shape_key.value = 0.0  # Initial value

                    # Get the vertex indices affected by this blendshape
                    vertex_indices = list(reader.getBlendShapeTargetVertexIndices(mesh_index, target_index))

                    # Get the delta values
                    delta_xs = list(reader.getBlendShapeTargetDeltaXs(mesh_index, target_index))
                    delta_ys = list(reader.getBlendShapeTargetDeltaYs(mesh_index, target_index))
                    delta_zs = list(reader.getBlendShapeTargetDeltaZs(mesh_index, target_index))

                    print(f"Blendshape {channel_name} affects {len(vertex_indices)} vertices")

                    # Apply the deltas to the shape key
                    for i, vertex_index in enumerate(vertex_indices):
                        if vertex_index < len(shape_key.data):
                            # Get the delta values and convert using our utility function
                            dx, dy, dz = dna_to_blender_coords(delta_xs[i], delta_ys[i], delta_zs[i])

                            # Apply the delta to the shape key
                            # Note: We're applying deltas to the basis shape, not setting absolute positions
                            shape_key.data[vertex_index].co.x += dx
                            shape_key.data[vertex_index].co.y += dy
                            shape_key.data[vertex_index].co.z += dz

                            # Log some sample deltas for debugging
                            if i < 5 or i == len(vertex_indices) - 1:
                                print(f"Delta for vertex {vertex_index}: ({dx}, {dy}, {dz})")

                    print(f"Created blendshape {channel_name} with {len(vertex_indices)} vertex deltas")

                except Exception as e:
                    print(f"Error creating blendshape target {target_index}: {e}")
                    import traceback
                    traceback.print_exc()

        except Exception as e:
            print(f"Error creating blendshapes: {e}")
            import traceback
            traceback.print_exc()

    @staticmethod
    def init_uvs(mesh):
        """Initialize UV maps following example plugin approach

        Args:
            mesh: Blender mesh data
        """
        uv_layer = mesh.uv_layers.get(UV_MAP_NAME)
        if not uv_layer:
            uv_layer = mesh.uv_layers.new(name=UV_MAP_NAME)
        mesh.uv_layers.active = uv_layer

    def create_material_for_mesh(self, mesh_obj):
        """Create a basic material for the mesh - to be implemented later"""
        # This method will be implemented in a future update
        pass

# Classes to register
classes = [
    DNA_OT_CreateModel,
]

def register():
    """Register the create model operator"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the create model operator"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
