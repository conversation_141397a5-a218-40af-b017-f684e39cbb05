"""
Material utility functions for the Blender MetaHuman DNA Plugin.

This module provides functionality for creating and managing materials
following the exact same approach as the example plugin.
"""

import os
import sys
import bpy
import shutil
from pathlib import Path
from ..constants import RESOURCES_FOLDER, BLENDS_FOLDER

# Set up logging
import time

def log_info(message):
    """Log an info message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [INFO] {message}")

def log_warning(message):
    """Log a warning message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [WARNING] {message}")

def log_error(message):
    """Log an error message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [ERROR] {message}")

def log_debug(message):
    """Log a debug message with timestamp"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    print(f"[{timestamp}] [DEBUG] {message}")

# Material constants following the example plugin exactly
HEAD_MATERIAL_NAME = "head_shader"
MASKS_TEXTURE = "combined_masks.tga"
TOPOLOGY_TEXTURE = "head_topology.png"
TEXTURE_LOGIC_NODE_NAME = "texture_logic"
TEXTURE_LOGIC_NODE_LABEL = "Texture Logic"
UV_MAP_NAME = "DiffuseUV"
VERTEX_COLOR_ATTRIBUTE_NAME = "Color"
FLOATING_POINT_PRECISION = 0.0001

# Material file paths
MATERIALS_FILE_PATH = BLENDS_FOLDER / "materials.blend"
MASKS_TEXTURE_FILE_PATH = RESOURCES_FOLDER / "images" / MASKS_TEXTURE
TOPOLOGY_TEXTURE_FILE_PATH = RESOURCES_FOLDER / "images" / TOPOLOGY_TEXTURE

# Mesh to shader mapping following the example plugin exactly
MESH_SHADER_MAPPING = {
    "head_lod": "head_shader",
    "teeth_lod": "teeth_shader",
    "saliva_lod": "saliva_shader",
    "eyeLeft_lod": "eyeLeft_shader",
    "eyeRight_lod": "eyeRight_shader",
    "eyeshell_lod": "eyeshell_shader",
    "eyelashes_lod": "eyelashes_shader",
    "eyelashesShadow_lod": "eyelashesShadow_shader",
    "eyeEdge_lod": "eyeEdge_shader",
    "cartilage_lod": "cartilage_shader",
}

# Material slot to material instance defaults (for Unreal export compatibility)
MATERIAL_SLOT_TO_MATERIAL_INSTANCE_DEFAULTS = {
    "head_shader": "/Game/MetaHumans/Common/Face/Materials/Baked/MI_HeadSynthesized_Baked",
    "teeth_shader": "/Game/MetaHumans/Common/Materials/M_TeethCharacterCreator_Inst",
    "saliva_shader": "/Game/MetaHumans/Common/Face/Materials/MI_lacrimal_fluid_Inst",
    "eyeLeft_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeRefractive_Inst_L",
    "eyeRight_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeRefractive_Inst_R",
    "eyeshell_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeOcclusion_Inst",
    "eyelashes_shader": "/Game/MetaHumans/Common/Materials/M_EyelashLowerLODs_Inst",
    "eyelashesShadow_shader": "/Game/MetaHumans/Common/Face/Materials/MI_EyeOcclusion_Inst",
    "eyeEdge_shader": "/Game/MetaHumans/Common/Face/Materials/MI_lacrimal_fluid_Inst",
    "cartilage_shader": "/Game/MetaHumans/Common/Face/Materials/M_Cartilage",
}

# Head texture maps (for reference)
HEAD_MAPS = {
    "Color_MAIN": "head_color_map.tga",
    "Color_CM1": "head_cm1_color_map.tga",
    "Color_CM2": "head_cm2_color_map.tga",
    "Color_CM3": "head_cm3_color_map.tga",
    "Normal_MAIN": "head_normal_map.tga",
    "Normal_WM1": "head_wm1_normal_map.tga",
    "Normal_WM2": "head_wm2_normal_map.tga",
    "Normal_WM3": "head_wm3_normal_map.tga",
    "Cavity_MAIN": "head_cavity_map.tga",
    "Roughness_MAIN": "head_roughness_map.tga"
}

# Unreal exported head material names
UNREAL_EXPORTED_HEAD_MATERIAL_NAMES = [
    'MI_HeadSynthesized_Baked'
]

# Alternate texture file names for compatibility
ALTERNATE_TEXTURE_FILE_NAMES = {
    'head_color_map': ['head_diffuse_map', 'head_albedo_map'],
    'head_normal_map': ['head_norm_map', 'head_normalmap'],
    'head_roughness_map': ['head_rough_map', 'head_roughnessmap'],
    'head_cavity_map': ['head_ao_map', 'head_ambient_occlusion_map']
}

# Alternate texture file extensions
ALTERNATE_TEXTURE_FILE_EXTENSIONS = ['.tga', '.png', '.jpg', '.jpeg', '.exr']

def purge_existing_materials(model_name: str):
    """Remove existing materials for this model to avoid duplicates

    Args:
        model_name: Name of the model to purge materials for
    """
    log_info(f"Purging existing materials for model: {model_name}")

    # Remove existing materials for this model
    for material_name in MESH_SHADER_MAPPING.values():
        full_material_name = f'{model_name}_{material_name}'
        material = bpy.data.materials.get(full_material_name)
        if material:
            log_info(f"Removing existing material: {material.name}")
            bpy.data.materials.remove(material)

    # Remove existing mask and topology images to avoid duplicates
    masks_image = bpy.data.images.get(MASKS_TEXTURE)
    if masks_image:
        log_debug(f"Removing existing masks image: {masks_image.name}")
        bpy.data.images.remove(masks_image)

    topology_image = bpy.data.images.get(TOPOLOGY_TEXTURE)
    if topology_image:
        log_debug(f"Removing existing topology image: {topology_image.name}")
        bpy.data.images.remove(topology_image)

def create_new_material(name: str, color: tuple = None, alpha: float = None) -> bpy.types.Material:
    """Create a new material with basic settings following example plugin

    Args:
        name: Name of the material
        color: RGBA color tuple (optional)
        alpha: Alpha value (optional)

    Returns:
        Created material
    """
    log_debug(f"Creating new material: {name}")
    material = bpy.data.materials.new(name=name)
    material.use_nodes = True

    # Create a Principled BSDF shader node
    principled_bsdf = material.node_tree.nodes.get("Principled BSDF")
    if color and principled_bsdf:
        principled_bsdf.inputs['Base Color'].default_value = color
    if alpha is not None and principled_bsdf:
        principled_bsdf.inputs['Alpha'].default_value = alpha
        material.blend_method = 'BLEND'

    return material

def set_uv_maps_on_material(material: bpy.types.Material):
    """Set UV maps on material nodes to use the correct UV map name

    Args:
        material: Material to process
    """
    if not material.node_tree:
        return

    log_debug(f"Setting UV maps on material: {material.name}")

    # Set UV maps on material nodes
    for node in material.node_tree.nodes:
        if node.type == 'UVMAP':
            node.uv_map = UV_MAP_NAME
            log_debug(f"Set UV map '{UV_MAP_NAME}' on UV Map node: {node.name}")
        elif node.type == 'NORMAL_MAP':
            # Normal map nodes might have UV map settings
            if hasattr(node, 'uv_map'):
                node.uv_map = UV_MAP_NAME
                log_debug(f"Set UV map '{UV_MAP_NAME}' on Normal Map node: {node.name}")

def set_uv_maps_on_node_groups():
    """Set UV maps on node groups, specifically for mask nodes
    """
    log_debug("Setting UV maps on node groups")

    # Set UV maps on node groups (especially mask-related ones)
    for node_group in bpy.data.node_groups:
        if 'Mask' in node_group.name or 'mask' in node_group.name:
            log_debug(f"Processing node group: {node_group.name}")
            for node in node_group.nodes:
                if node.type == 'UVMAP':
                    node.uv_map = UV_MAP_NAME
                    log_debug(f"Set UV map '{UV_MAP_NAME}' on node group UV Map node: {node.name}")

def apply_material_to_mesh(model_name: str, mesh_key: str, material: bpy.types.Material):
    """Apply material to corresponding mesh objects with proper pattern matching

    Args:
        model_name: Name of the model
        mesh_key: Key from MESH_SHADER_MAPPING (e.g., "head_lod")
        material: Material to apply
    """
    # Convert mesh_key to the actual mesh naming pattern
    # mesh_key like "head_lod" should match "head_lod0_mesh"
    mesh_pattern = mesh_key.replace('_lod', '_lod0') + '_mesh'

    # First try exact matching
    mesh_object = bpy.data.objects.get(mesh_pattern)
    if mesh_object and mesh_object.type == 'MESH':
        log_info(f"Applying material {material.name} to mesh {mesh_object.name} (exact match)")
        # Clear existing materials and add the new one
        mesh_object.data.materials.clear()
        mesh_object.data.materials.append(material)
        return

    # If exact match not found, try partial matching
    mesh_base = mesh_key.replace('_lod', '')
    for obj in bpy.data.objects:
        if obj.type == 'MESH' and mesh_base in obj.name and obj.name.endswith('_mesh'):
            log_info(f"Applying material {material.name} to mesh {obj.name} (partial match)")
            # Clear existing materials and add the new one
            obj.data.materials.clear()
            obj.data.materials.append(material)
            return

    log_warning(f"No mesh found for material {material.name} with pattern {mesh_pattern}")

def copy_materials(mesh_object: bpy.types.Object, old_prefix: str, new_prefix: str, new_folder: Path) -> bpy.types.Material:
    """Copy materials from one mesh to another with new prefix

    Args:
        mesh_object: Mesh object to copy materials from
        old_prefix: Old prefix to replace
        new_prefix: New prefix to use
        new_folder: Folder for new material files

    Returns:
        First new mesh material
    """
    first_new_mesh_material = None
    for slot in mesh_object.material_slots:
        material = slot.material
        if material:
            new_material = material.copy()
            new_material.name = material.name.replace(old_prefix, new_prefix)
            slot.material = new_material
            if not first_new_mesh_material:
                first_new_mesh_material = new_material

            # duplicate the image nodes
            if new_material.node_tree:
                for node in new_material.node_tree.nodes:
                    if node.type == 'TEX_IMAGE':
                        new_image = node.image.copy()
                        new_image.name = f'{new_prefix}_{node.image.name}'.replace(f'{old_prefix}_', '')
                        # copy the image files to the new folder
                        if new_image.filepath and not new_image.packed_file:
                            image_file_path = Path(bpy.path.abspath(new_image.filepath))
                            if image_file_path.exists():
                                new_image_file_path = new_folder / 'maps' / image_file_path.name
                                new_image_file_path.parent.mkdir(parents=True, exist_ok=True)
                                shutil.copy(image_file_path, new_image_file_path)
                                new_image.filepath = str(new_image_file_path)
                        # assign the new image to the node
                        node.image = new_image
    return first_new_mesh_material

def set_viewport_shading(shading_type: str):
    """Set the viewport shading mode following example plugin

    Args:
        shading_type: Type of shading ('MATERIAL', 'SOLID', 'WIREFRAME', 'RENDERED')
    """
    log_debug(f"Setting viewport shading to: {shading_type}")
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            for space in area.spaces:
                if space.type == 'VIEW_3D':
                    space.shading.type = shading_type
                    break

def prefix_material_image_names(material: bpy.types.Material, prefix: str):
    """Prefix material image names with the given prefix following example plugin

    Args:
        material: Material to process
        prefix: Prefix to add to image names
    """
    if not material.node_tree:
        return

    log_debug(f"Prefixing image names in material {material.name} with '{prefix}'")
    for node in material.node_tree.nodes:
        if node.type == 'TEX_IMAGE' and node.image:
            if not node.image.name.startswith(prefix):
                old_name = node.image.name
                node.image.name = f"{prefix}_{node.image.name}"
                log_debug(f"Renamed image: {old_name} → {node.image.name}")

def get_alternate_image_path(image_path: Path) -> Path:
    """Get alternate image path if the original doesn't exist following example plugin

    Args:
        image_path: Original image path

    Returns:
        Alternate image path or original if no alternate found
    """
    if image_path.exists():
        return image_path

    log_debug(f"Searching for alternate image path for: {image_path}")

    # Try alternate names
    stem = image_path.stem
    if stem in ALTERNATE_TEXTURE_FILE_NAMES:
        for alt_name in ALTERNATE_TEXTURE_FILE_NAMES[stem]:
            alt_path = image_path.parent / f"{alt_name}{image_path.suffix}"
            if alt_path.exists():
                log_debug(f"Found alternate image: {alt_path}")
                return alt_path

    # Try alternate extensions
    for ext in ALTERNATE_TEXTURE_FILE_EXTENSIONS:
        alt_path = image_path.parent / f"{stem}{ext}"
        if alt_path.exists():
            log_debug(f"Found alternate image with different extension: {alt_path}")
            return alt_path

    log_warning(f"No alternate image found for: {image_path}")
    return image_path

def load_mask_and_topology_textures():
    """Load mask and topology textures from resource files following example plugin

    Returns:
        Tuple of (masks_image, topology_image) or (None, None) if not found
    """
    masks_image = None
    topology_image = None

    # Load combined mask image
    if MASKS_TEXTURE_FILE_PATH.exists():
        try:
            masks_image = bpy.data.images.load(str(MASKS_TEXTURE_FILE_PATH))
            log_info(f"Loaded masks texture: {MASKS_TEXTURE_FILE_PATH}")
        except Exception as e:
            log_error(f"Failed to load masks texture: {e}")
    else:
        log_warning(f"Masks texture not found: {MASKS_TEXTURE_FILE_PATH}")

    # Load topology image
    if TOPOLOGY_TEXTURE_FILE_PATH.exists():
        try:
            topology_image = bpy.data.images.load(str(TOPOLOGY_TEXTURE_FILE_PATH))
            log_info(f"Loaded topology texture: {TOPOLOGY_TEXTURE_FILE_PATH}")
        except Exception as e:
            log_error(f"Failed to load topology texture: {e}")
    else:
        log_warning(f"Topology texture not found: {TOPOLOGY_TEXTURE_FILE_PATH}")

    return masks_image, topology_image

def set_image_textures_for_materials(materials: list, model_name: str, maps_folder: Path):
    """Set image textures for materials from maps folder following example plugin

    Args:
        materials: List of materials to process
        model_name: Name of the model
        maps_folder: Path to the maps folder
    """
    log_info(f"Setting image textures for {len(materials)} materials from maps folder: {maps_folder}")

    for material in materials:
        if not material.node_tree:
            continue

        log_debug(f"Processing textures for material: {material.name}")

        for node in material.node_tree.nodes:
            if node.type == 'TEX_IMAGE' and node.image:
                # Get the image file name without the postfixes for duplicates
                image_file = node.image.name
                if image_file.count('.') > 1:
                    image_file = image_file.rsplit('.', 1)[0]

                # Update the texture paths to images in the maps folder
                new_image_path = maps_folder / image_file

                # Check for alternate image file names
                new_image_path = get_alternate_image_path(new_image_path)

                if new_image_path.exists():
                    try:
                        node.image = bpy.data.images.load(str(new_image_path))
                        log_info(f"Loaded texture: {new_image_path}")

                        # Reloading images defaults the color space, so reset normal map to Non-Color
                        if new_image_path.stem.endswith('normal_map'):
                            node.image.colorspace_settings.name = 'Non-Color'
                            log_debug(f"Set color space to Non-Color for normal map: {new_image_path.name}")
                    except Exception as e:
                        log_error(f"Failed to load texture {new_image_path}: {e}")
                else:
                    log_warning(f"Texture not found: {new_image_path}")

def update_node_group_textures(masks_image, topology_image):
    """Update node group textures for masks and topology following example plugin

    Args:
        masks_image: Loaded masks image
        topology_image: Loaded topology image
    """
    log_debug("Updating node group textures")

    # Set the masks and topology textures for all node groups
    for node_group in bpy.data.node_groups:
        for node in node_group.nodes:
            if node.type == 'TEX_IMAGE' and node.image:
                # Set the masks and topology textures
                if node.image.name == MASKS_TEXTURE and masks_image:
                    node.image = masks_image
                    log_debug(f"Updated masks texture in node group: {node_group.name}")
                if node.image.name == TOPOLOGY_TEXTURE and topology_image:
                    node.image = topology_image
                    log_debug(f"Updated topology texture in node group: {node_group.name}")

def get_platform_specific_path_separator() -> str:
    """Get platform-specific path separator following example plugin

    Returns:
        Path separator string
    """
    return '\\' if sys.platform == 'win32' else '/'
