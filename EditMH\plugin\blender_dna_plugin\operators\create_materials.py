"""
Create Materials Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to create materials for DNA meshes
following the exact same approach as the example plugin.
"""

import os
import sys
import bpy
import time
import traceback
from bpy.props import StringProperty, BoolProperty
from pathlib import Path

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, ensure_dna_modules_path
from ..utils.ui_utils import update_ui
from ..utils.material_utils import (
    MATERIALS_FILE_PATH, MASKS_TEXTURE_FILE_PATH, TOPOLOGY_TEXTURE_FILE_PATH,
    MESH_SHADER_MAPPING, HEAD_MATERIAL_NAME, MASKS_TEXTURE, TOPOLOGY_TEXTURE,
    TEXTURE_LOGIC_NODE_NAME, UV_MAP_NAME, MATERIAL_SLOT_TO_MATERIAL_INSTANCE_DEFAULTS,
    purge_existing_materials, create_new_material, set_viewport_shading,
    prefix_material_image_names, get_alternate_image_path, set_uv_maps_on_material,
    set_uv_maps_on_node_groups, apply_material_to_mesh, load_mask_and_topology_textures,
    set_image_textures_for_materials, update_node_group_textures, get_platform_specific_path_separator,
    log_info, log_warning, log_error, log_debug
)

class DNA_OT_CreateMaterials(bpy.types.Operator):
    """Create materials for the DNA meshes"""
    bl_idname = "dna.create_materials"
    bl_label = "Create Materials"
    bl_description = "Create materials for the DNA meshes from the materials blend file"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        """Execute the create materials operation"""
        log_info("=== DNA Create Materials Process Started ===")

        # Update status message
        context.scene.dna_tools.status_message = "Creating materials..."
        update_ui()

        # Check if a DNA file is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            log_error("No DNA file loaded, cancelling material creation")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Check if mesh has been created
        if not dna_tools.is_mesh_created:
            log_error("No mesh created, cancelling material creation")
            context.scene.dna_tools.status_message = "Error: No mesh created"
            update_ui()
            self.report({'ERROR'}, "No mesh created. Create a mesh first.")
            return {'CANCELLED'}

        # Check if weights have been applied
        if not dna_tools.is_weights_applied:
            log_error("Weights not applied, cancelling material creation")
            context.scene.dna_tools.status_message = "Error: Weights not applied"
            update_ui()
            self.report({'ERROR'}, "Weights not applied. Apply weights first.")
            return {'CANCELLED'}

        # Create materials
        try:
            log_info("Creating materials from blend file...")
            context.scene.dna_tools.status_message = "Creating materials from blend file..."
            update_ui()

            self.create_materials_from_blend(context)

            # Set the flag to indicate materials have been created
            context.scene.dna_tools.is_materials_created = True

            log_info("Material creation completed successfully")
            context.scene.dna_tools.status_message = "Material creation completed successfully"
            update_ui()

            return {'FINISHED'}
        except Exception as e:
            log_error(f"Error during material creation: {str(e)}")
            traceback.print_exc()
            context.scene.dna_tools.status_message = f"Error during material creation: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error creating materials: {str(e)}")
            return {'CANCELLED'}

    def create_materials_from_blend(self, context):
        """Create materials from the materials blend file following example plugin exactly"""
        log_info("Starting material creation process")

        # Get the DNA file path to determine the model name
        dna_file_path = context.scene.dna_tools.dna_file_path
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
        log_info(f"Model name: {model_name}")

        # Find the parent collection
        parent_collection = None
        for collection in bpy.data.collections:
            if collection.name == model_name:
                parent_collection = collection
                log_info(f"Found existing collection: {model_name}")
                break

        if not parent_collection:
            log_error(f"Collection {model_name} not found")
            raise Exception(f"Collection {model_name} not found")

        # Check if materials blend file exists
        if not MATERIALS_FILE_PATH.exists():
            log_error(f"Materials blend file not found: {MATERIALS_FILE_PATH}")
            raise Exception(f"Materials blend file not found: {MATERIALS_FILE_PATH}")

        log_info(f"Using materials blend file: {MATERIALS_FILE_PATH}")

        # Set the active collection to the scene collection
        bpy.context.view_layer.active_layer_collection = bpy.context.view_layer.layer_collection

        # Remove existing matching materials for this model to avoid duplicates
        purge_existing_materials(model_name)

        # Import materials from the blend file with platform-specific path handling
        materials = []
        sep = get_platform_specific_path_separator()
        directory_path = f'{MATERIALS_FILE_PATH}{sep}Material{sep}'
        log_info(f"Material directory path: {directory_path}")

        for mesh_key, material_name in MESH_SHADER_MAPPING.items():
            material = bpy.data.materials.get(material_name)
            if not material:
                # Import the materials using bpy.ops.wm.append
                file_path = f'{MATERIALS_FILE_PATH}{sep}Material{sep}{material_name}'
                log_info(f"Importing material from: {file_path}")

                try:
                    bpy.ops.wm.append(
                        filepath=file_path,
                        filename=material_name,
                        directory=directory_path
                    )
                    log_info(f"Successfully imported material: {material_name}")
                except Exception as e:
                    log_warning(f"Failed to import material {material_name}: {str(e)}")
                    continue

                # Get the imported material
                material = bpy.data.materials.get(material_name)
                if not material:
                    material = bpy.data.materials.get(f'{model_name}_{material_name}')

                # Create transparent materials if they don't exist (for eyes, saliva, etc.)
                if not material:
                    log_info(f"Creating transparent material: {model_name}_{material_name}")
                    material = create_new_material(
                        name=f'{model_name}_{material_name}',
                        color=(1.0, 1.0, 1.0, 0.0),
                        alpha=0.0
                    )

                # Rename to match metahuman naming convention with model prefix
                if material:
                    material.name = f'{model_name}_{material_name}'
                    log_info(f"Renamed material to: {material.name}")

                    # Set the UV maps on the material nodes
                    set_uv_maps_on_material(material)

                    # Apply material to corresponding mesh objects
                    apply_material_to_mesh(model_name, mesh_key, material)

            if material:
                materials.append(material)

        # Set UV maps on node groups (for mask nodes)
        set_uv_maps_on_node_groups()

        # Switch to material view
        set_viewport_shading('MATERIAL')

        # Load mask and topology textures from resource files
        masks_image, topology_image = load_mask_and_topology_textures()

        # Get the maps folder (assuming it's next to the DNA file)
        maps_folder = Path(dna_file_path).parent / "maps"

        # Set image textures for materials from maps folder
        set_image_textures_for_materials(materials, model_name, maps_folder)

        # Update node group textures for masks and topology
        update_node_group_textures(masks_image, topology_image)

        # Prefix the material image names with the metahuman name
        for material in materials:
            prefix_material_image_names(material=material, prefix=model_name)

        log_info(f"Material creation completed for {len(materials)} materials")
        return materials

# All helper methods have been moved to material_utils.py and are imported above

# Classes to register
classes = [
    DNA_OT_CreateMaterials,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)
    print("DNA Create Materials Operator registered")

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    print("DNA Create Materials Operator unregistered")
