"""
Create Materials Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to create materials for DNA meshes
following the same approach as the example plugin.
"""

import os
import sys
import bpy
import time
import traceback
from bpy.props import StringProperty, BoolProperty
from pathlib import Path

# Import the DNA utils
from ..utils.dna_utils import check_dna_modules, ensure_dna_modules_path
from ..utils.ui_utils import update_ui
from ..utils.material_utils import (
    MATERIALS_FILE_PATH, MASKS_TEXTURE_FILE_PATH, TOPOLOGY_TEXTURE_FILE_PATH,
    MESH_SHADER_MAPPING, HEAD_MATERIAL_NAME, MASKS_TEXTURE, TOPOLOGY_TEXTURE,
    TEXTURE_LOGIC_NODE_NAME, UV_MAP_NAME, create_new_material, set_viewport_shading,
    prefix_material_image_names, get_alternate_image_path, log_info, log_warning, log_error, log_debug
)

class DNA_OT_CreateMaterials(bpy.types.Operator):
    """Create materials for the DNA meshes"""
    bl_idname = "dna.create_materials"
    bl_label = "Create Materials"
    bl_description = "Create materials for the DNA meshes from the materials blend file"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        """Execute the create materials operation"""
        log_info("=== DNA Create Materials Process Started ===")

        # Update status message
        context.scene.dna_tools.status_message = "Creating materials..."
        update_ui()

        # Check if a DNA file is loaded
        dna_tools = context.scene.dna_tools
        if not dna_tools.is_dna_loaded:
            log_error("No DNA file loaded, cancelling material creation")
            context.scene.dna_tools.status_message = "Error: No DNA file loaded"
            update_ui()
            self.report({'ERROR'}, "No DNA file loaded. Import a DNA file first.")
            return {'CANCELLED'}

        # Check if mesh has been created
        if not dna_tools.is_mesh_created:
            log_error("No mesh created, cancelling material creation")
            context.scene.dna_tools.status_message = "Error: No mesh created"
            update_ui()
            self.report({'ERROR'}, "No mesh created. Create a mesh first.")
            return {'CANCELLED'}

        # Check if weights have been applied
        if not dna_tools.is_weights_applied:
            log_error("Weights not applied, cancelling material creation")
            context.scene.dna_tools.status_message = "Error: Weights not applied"
            update_ui()
            self.report({'ERROR'}, "Weights not applied. Apply weights first.")
            return {'CANCELLED'}

        # Create materials
        try:
            log_info("Creating materials from blend file...")
            context.scene.dna_tools.status_message = "Creating materials from blend file..."
            update_ui()

            self.create_materials_from_blend(context)

            # Set the flag to indicate materials have been created
            context.scene.dna_tools.is_materials_created = True

            log_info("Material creation completed successfully")
            context.scene.dna_tools.status_message = "Material creation completed successfully"
            update_ui()

            return {'FINISHED'}
        except Exception as e:
            log_error(f"Error during material creation: {str(e)}")
            traceback.print_exc()
            context.scene.dna_tools.status_message = f"Error during material creation: {str(e)}"
            update_ui()
            self.report({'ERROR'}, f"Error creating materials: {str(e)}")
            return {'CANCELLED'}

    def create_materials_from_blend(self, context):
        """Create materials from the materials blend file"""
        log_info("Starting material creation process")

        # Get the DNA file path to determine the model name
        dna_file_path = context.scene.dna_tools.dna_file_path
        model_name = os.path.splitext(os.path.basename(dna_file_path))[0]
        log_info(f"Model name: {model_name}")

        # Find the parent collection
        parent_collection = None
        for collection in bpy.data.collections:
            if collection.name == model_name:
                parent_collection = collection
                log_info(f"Found existing collection: {model_name}")
                break

        if not parent_collection:
            log_error(f"Collection {model_name} not found")
            raise Exception(f"Collection {model_name} not found")

        # Check if materials blend file exists
        if not MATERIALS_FILE_PATH.exists():
            log_error(f"Materials blend file not found: {MATERIALS_FILE_PATH}")
            raise Exception(f"Materials blend file not found: {MATERIALS_FILE_PATH}")

        log_info(f"Using materials blend file: {MATERIALS_FILE_PATH}")

        # Set the active collection to the scene collection
        bpy.context.view_layer.active_layer_collection = bpy.context.view_layer.layer_collection

        # Remove existing matching materials for this model to avoid duplicates
        self._purge_existing_materials(model_name)

        # Import materials from the blend file
        materials = []
        sep = '\\'
        if sys.platform != 'win32':
            sep = '/'

        directory_path = f'{MATERIALS_FILE_PATH}{sep}Material{sep}'
        log_info(f"Material directory path: {directory_path}")

        for key, material_name in MESH_SHADER_MAPPING.items():
            material = bpy.data.materials.get(material_name)
            if not material:
                # Import the materials
                file_path = f'{MATERIALS_FILE_PATH}{sep}Material{sep}{material_name}'
                log_info(f"Importing material from: {file_path}")

                try:
                    bpy.ops.wm.append(
                        filepath=file_path,
                        filename=material_name,
                        directory=directory_path
                    )
                    log_info(f"Successfully imported material: {material_name}")
                except Exception as e:
                    log_warning(f"Failed to import material {material_name}: {str(e)}")
                    continue

                # Get the imported material
                material = bpy.data.materials.get(material_name)
                if not material:
                    material = bpy.data.materials.get(f'{model_name}_{material_name}')
                    # Create transparent materials if they don't exist (for eyes and saliva)
                    if not material:
                        log_info(f"Creating transparent material: {model_name}_{material_name}")
                        material = create_new_material(
                            name=f'{model_name}_{material_name}',
                            color=(1.0, 1.0, 1.0, 0.0),
                            alpha=0.0
                        )

                # Rename to match metahuman naming convention
                if material:
                    material.name = f'{model_name}_{material_name}'
                    log_info(f"Renamed material to: {material.name}")

                    # Set the UV maps on the material nodes
                    self._set_uv_maps_on_material(material)

                    # Apply material to corresponding mesh objects
                    self._apply_material_to_meshes(parent_collection, model_name, key, material)

            if material:
                materials.append(material)

        # Switch to material view
        set_viewport_shading('MATERIAL')

        # Set the image textures to match
        self._set_image_textures(materials, model_name)

        # Prefix the material image names with the metahuman name
        for material in materials:
            prefix_material_image_names(material=material, prefix=model_name)

        log_info(f"Material creation completed for {len(materials)} materials")
        return materials

    def _purge_existing_materials(self, model_name):
        """Remove existing materials for this model"""
        log_info(f"Purging existing materials for model: {model_name}")

        for material_name in MESH_SHADER_MAPPING.values():
            material = bpy.data.materials.get(f'{model_name}_{material_name}')
            if material:
                log_info(f"Removing existing material: {material.name}")
                bpy.data.materials.remove(material)

        # Remove existing mask and topology images
        masks_image = bpy.data.images.get(MASKS_TEXTURE)
        if masks_image:
            bpy.data.images.remove(masks_image)

        topology_image = bpy.data.images.get(TOPOLOGY_TEXTURE)
        if topology_image:
            bpy.data.images.remove(topology_image)

    def _set_uv_maps_on_material(self, material):
        """Set UV maps on material nodes"""
        if not material.node_tree:
            return

        for node in material.node_tree.nodes:
            if node.type == 'UVMAP':
                node.uv_map = UV_MAP_NAME
            elif node.type == 'NORMAL_MAP':
                node.uv_map = UV_MAP_NAME

        # Set UV maps on node groups
        for node_group in bpy.data.node_groups:
            if node_group.name.startswith('Mask'):
                for node in node_group.nodes:
                    if node.type == 'UVMAP':
                        node.uv_map = UV_MAP_NAME

    def _apply_material_to_meshes(self, parent_collection, model_name, mesh_key, material):
        """Apply material to corresponding mesh objects"""
        # The actual mesh names are like "head_lod0_mesh", not "MH_Friend_head_lod"
        # So we need to match based on the mesh_key pattern

        # Convert mesh_key to the actual mesh naming pattern
        # mesh_key like "head_lod" should match "head_lod0_mesh"
        mesh_pattern = mesh_key.replace('_lod', '_lod0') + '_mesh'

        for mesh_object in bpy.data.objects:
            if mesh_object.type == 'MESH' and mesh_object.name == mesh_pattern:
                log_info(f"Applying material {material.name} to mesh {mesh_object.name}")
                # Clear existing materials and add the new one
                mesh_object.data.materials.clear()
                mesh_object.data.materials.append(material)
                break
        else:
            # If exact match not found, try partial matching
            for mesh_object in bpy.data.objects:
                if mesh_object.type == 'MESH' and mesh_key.replace('_lod', '') in mesh_object.name:
                    log_info(f"Applying material {material.name} to mesh {mesh_object.name} (partial match)")
                    # Clear existing materials and add the new one
                    mesh_object.data.materials.clear()
                    mesh_object.data.materials.append(material)
                    break

    def _set_image_textures(self, materials, model_name):
        """Set image textures for materials"""
        log_info("Setting image textures for materials")

        # Set the combined mask image and topology image
        if MASKS_TEXTURE_FILE_PATH.exists():
            bpy.data.images.load(str(MASKS_TEXTURE_FILE_PATH))
            log_info(f"Loaded masks texture: {MASKS_TEXTURE_FILE_PATH}")

        if TOPOLOGY_TEXTURE_FILE_PATH.exists():
            bpy.data.images.load(str(TOPOLOGY_TEXTURE_FILE_PATH))
            log_info(f"Loaded topology texture: {TOPOLOGY_TEXTURE_FILE_PATH}")

        # Get the maps folder (assuming it's next to the DNA file)
        dna_file_path = bpy.context.scene.dna_tools.dna_file_path
        maps_folder = Path(dna_file_path).parent / "maps"

        for material in materials:
            if not material.node_tree:
                continue

            for node in material.node_tree.nodes:
                if node.type == 'TEX_IMAGE' and node.image:
                    # Get the image file name without the postfixes for duplicates
                    image_file = node.image.name
                    if image_file.count('.') > 1:
                        image_file = image_file.rsplit('.', 1)[0]

                    # Update the texture paths to images in the maps folder
                    new_image_path = maps_folder / image_file

                    # Check for alternate image file names
                    new_image_path = get_alternate_image_path(new_image_path)

                    if new_image_path.exists():
                        node.image = bpy.data.images.load(str(new_image_path))
                        log_info(f"Loaded texture: {new_image_path}")

                        # Reloading images defaults the color space, so reset normal map to Non-Color
                        if new_image_path.stem.endswith('normal_map'):
                            node.image.colorspace_settings.name = 'Non-Color'

        # Set the masks and topology textures for all node groups
        for node_group in bpy.data.node_groups:
            for node in node_group.nodes:
                if node.type == 'TEX_IMAGE' and node.image:
                    # Set the masks and topology textures
                    if node.image.name == MASKS_TEXTURE:
                        masks_image = bpy.data.images.get(MASKS_TEXTURE)
                        if masks_image:
                            node.image = masks_image
                    if node.image.name == TOPOLOGY_TEXTURE:
                        topology_image = bpy.data.images.get(TOPOLOGY_TEXTURE)
                        if topology_image:
                            node.image = topology_image

# Classes to register
classes = [
    DNA_OT_CreateMaterials,
]

def register():
    """Register the operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)
    print("DNA Create Materials Operator registered")

def unregister():
    """Unregister the operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
    print("DNA Create Materials Operator unregistered")
