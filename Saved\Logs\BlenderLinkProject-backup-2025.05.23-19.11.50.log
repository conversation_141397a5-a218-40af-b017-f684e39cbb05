﻿Log file open, 05/23/25 21:23:32
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=35336)
LogWindows: Warning: Failed to set completion port for job object "UE.ShaderCompileWorker.JobGroup": The parameter is incorrect.
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 39775
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.217513
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-A8E255AC42C636330B44D28B2698A77E
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.06 seconds
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogConfig: Display: Loading Android ini files took 0.06 seconds
LogConfig: Display: Loading IOS ini files took 0.06 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogConfig: Display: Loading Unix ini files took 0.07 seconds
LogConfig: Display: Loading Windows ini files took 0.07 seconds
LogConfig: Display: Loading TVOS ini files took 0.07 seconds
LogConfig: Display: Loading VisionOS ini files took 0.07 seconds
LogConfig: Display: Loading Linux ini files took 0.07 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.07 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.53ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.06ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.06ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.23-15.53.32:981][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.23-15.53.32:981][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.23-15.53.32:981][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.23-15.53.32:981][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.23-15.53.32:981][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.23-15.53.32:981][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.23-15.53.32:982][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.23-15.53.32:982][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.23-15.53.32:982][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.23-15.53.32:985][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.23-15.53.32:985][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.23-15.53.32:985][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.23-15.53.32:986][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.23-15.53.32:986][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.23-15.53.32:989][  0]LogRHI: Using Default RHI: D3D12
[2025.05.23-15.53.32:989][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.23-15.53.32:989][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.23-15.53.32:992][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.23-15.53.32:992][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.23-15.53.33:077][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.23-15.53.33:077][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.23-15.53.33:077][  0]LogD3D12RHI:   Adapter has 16337MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.23-15.53.33:077][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.23-15.53.33:077][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.23-15.53.33:086][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.23-15.53.33:086][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.23-15.53.33:086][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.23-15.53.33:086][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.23-15.53.33:086][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.23-15.53.33:086][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.23-15.53.33:086][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.23-15.53.33:086][  0]LogHAL: Display: Platform has ~ 64 GB [68632784896 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.23-15.53.33:087][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.23-15.53.33:087][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.23-15.53.33:087][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.23-15.53.33:087][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.23-15.53.33:087][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.23-15.53.33:087][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.23-15.53.33:087][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.23-15.53.33:087][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.23-15.53.33:087][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.23-15.53.33:087][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.23-15.53.33:087][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.23-15.53.33:087][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.23-15.53.33:087][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.23-15.53.33:087][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.23-15.53.33:087][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.23-15.53.33:087][  0]LogInit: User: Shashank
[2025.05.23-15.53.33:087][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.23-15.53.33:087][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.23-15.53.33:348][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.23-15.53.33:348][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.23-15.53.33:348][  0]LogMemory: Process Physical Memory: 607.34 MB used, 628.51 MB peak
[2025.05.23-15.53.33:348][  0]LogMemory: Process Virtual Memory: 672.93 MB used, 672.93 MB peak
[2025.05.23-15.53.33:348][  0]LogMemory: Physical Memory: 26827.77 MB used,  38625.55 MB free, 65453.32 MB total
[2025.05.23-15.53.33:348][  0]LogMemory: Virtual Memory: 36564.91 MB used,  32984.42 MB free, 69549.33 MB total
[2025.05.23-15.53.33:348][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.23-15.53.33:351][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.23-15.53.33:357][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.23-15.53.33:357][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.23-15.53.33:358][  0]LogInit: Using OS detected language (en-GB).
[2025.05.23-15.53.33:358][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.23-15.53.33:359][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.23-15.53.33:359][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.23-15.53.33:628][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.23-15.53.33:628][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.23-15.53.33:628][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.23-15.53.33:640][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.23-15.53.33:640][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.23-15.53.33:719][  0]LogRHI: Using Default RHI: D3D12
[2025.05.23-15.53.33:719][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.23-15.53.33:719][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.23-15.53.33:719][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.23-15.53.33:719][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.23-15.53.33:719][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.23-15.53.33:720][  0]LogWindows: Attached monitors:
[2025.05.23-15.53.33:720][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.23-15.53.33:720][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY2' [PRIMARY]
[2025.05.23-15.53.33:720][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY3'
[2025.05.23-15.53.33:720][  0]LogWindows: Found 3 attached monitors.
[2025.05.23-15.53.33:720][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.23-15.53.33:720][  0]LogRHI: RHI Adapter Info:
[2025.05.23-15.53.33:720][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.23-15.53.33:720][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.23-15.53.33:720][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.23-15.53.33:720][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.23-15.53.33:746][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.23-15.53.33:760][  0]LogNvidiaAftermath: Aftermath crash dumping failed to initialize (bad0000c).
[2025.05.23-15.53.33:760][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.23-15.53.33:829][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: Raster order views are supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.23-15.53.33:829][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000009378AD05300)
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000009378AD05580)
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000009378AD05800)
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.23-15.53.33:853][  0]LogRHI: Texture pool is 9808 MB (70% of 14012 MB)
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.23-15.53.33:853][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.23-15.53.33:865][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.23-15.53.33:869][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.23-15.53.33:875][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.23-15.53.33:875][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.23-15.53.33:901][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.23-15.53.33:901][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.23-15.53.33:901][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.23-15.53.33:901][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.23-15.53.33:901][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.23-15.53.33:901][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.23-15.53.33:901][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.23-15.53.33:901][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.23-15.53.33:902][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.23-15.53.33:924][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.23-15.53.33:936][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.23-15.53.33:936][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.23-15.53.33:948][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.23-15.53.33:948][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.23-15.53.33:948][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.23-15.53.33:948][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.23-15.53.33:960][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.23-15.53.33:960][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.23-15.53.33:960][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.23-15.53.33:972][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.23-15.53.33:972][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.23-15.53.33:972][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.23-15.53.33:972][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.23-15.53.33:984][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.23-15.53.33:984][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.23-15.53.33:997][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.23-15.53.33:997][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.23-15.53.33:997][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.23-15.53.33:997][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.23-15.53.33:997][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.23-15.53.34:033][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.23-15.53.34:035][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.23-15.53.34:035][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.23-15.53.34:035][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.23-15.53.34:037][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.23-15.53.34:037][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.23-15.53.34:037][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.23-15.53.34:037][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.23-15.53.34:037][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.23-15.53.34:098][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.23-15.53.34:098][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.23-15.53.34:098][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.23-15.53.34:098][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.23-15.53.34:099][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.23-15.53.34:099][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.23-15.53.34:099][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.23-15.53.34:099][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 42588 --child-id Zen_42588_Startup'
[2025.05.23-15.53.34:143][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.23-15.53.34:143][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.045 seconds
[2025.05.23-15.53.34:145][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.23-15.53.34:148][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.23-15.53.34:148][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.01ms. RandomReadSpeed=2327.13MBs, RandomWriteSpeed=395.66MBs. Assigned SpeedClass 'Local'
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.23-15.53.34:149][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.23-15.53.34:149][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/42588/).
[2025.05.23-15.53.34:149][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/009EFAED4DBA4FCE252900AF99C4FE97/'.
[2025.05.23-15.53.34:149][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.23-15.53.34:149][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.23-15.53.34:151][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.23-15.53.34:151][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.23-15.53.34:516][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.23-15.53.35:020][  0]LogSlate: Using FreeType 2.10.0
[2025.05.23-15.53.35:020][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.23-15.53.35:020][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.23-15.53.35:020][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.23-15.53.35:020][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.23-15.53.35:020][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.23-15.53.35:020][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.23-15.53.35:020][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.23-15.53.35:041][  0]LogAssetRegistry: FAssetRegistry took 0.0018 seconds to start up
[2025.05.23-15.53.35:042][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.23-15.53.35:047][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.23-15.53.35:047][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.23-15.53.35:211][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.23-15.53.35:212][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.23-15.53.35:212][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.23-15.53.35:212][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.23-15.53.35:222][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.23-15.53.35:222][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.23-15.53.35:244][  0]LogDeviceProfileManager: Active device profile: [00000937AC5EBA00][00000937AA700000 66] WindowsEditor
[2025.05.23-15.53.35:244][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.23-15.53.35:245][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.23-15.53.35:247][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.23-15.53.35:247][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.23-15.53.35:274][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.23-15.53.35:275][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.23-15.53.35:276][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.23-15.53.35:277][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.23-15.53.35:278][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.23-15.53.35:416][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.23-15.53.35:416][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.23-15.53.35:416][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.23-15.53.35:416][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.23-15.53.35:416][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.23-15.53.35:525][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.23-15.53.35:542][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.23-15.53.35:551][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.41ms
[2025.05.23-15.53.35:553][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.23-15.53.35:720][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.23-15.53.35:720][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.23-15.53.35:723][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.23-15.53.35:723][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.23-15.53.35:725][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.23-15.53.35:727][  0]LogLiveCoding: Display: Waiting for server
[2025.05.23-15.53.35:739][  0]LogSlate: Border
[2025.05.23-15.53.35:739][  0]LogSlate: BreadcrumbButton
[2025.05.23-15.53.35:739][  0]LogSlate: Brushes.Title
[2025.05.23-15.53.35:739][  0]LogSlate: Default
[2025.05.23-15.53.35:739][  0]LogSlate: Icons.Save
[2025.05.23-15.53.35:739][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.23-15.53.35:739][  0]LogSlate: ListView
[2025.05.23-15.53.35:739][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.23-15.53.35:739][  0]LogSlate: SoftwareCursor_Grab
[2025.05.23-15.53.35:739][  0]LogSlate: TableView.DarkRow
[2025.05.23-15.53.35:739][  0]LogSlate: TableView.Row
[2025.05.23-15.53.35:739][  0]LogSlate: TreeView
[2025.05.23-15.53.35:771][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.23-15.53.35:794][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.23-15.53.35:796][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.674 ms
[2025.05.23-15.53.35:803][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.42ms
[2025.05.23-15.53.35:815][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.23-15.53.35:815][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.23-15.53.35:815][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.23-15.53.35:815][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.23-15.53.36:130][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.23-15.53.36:132][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.23-15.53.36:146][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.23-15.53.36:278][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.23-15.53.36:278][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.23-15.53.36:294][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.23-15.53.36:306][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.45ms
[2025.05.23-15.53.36:392][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 54814208BB24413B8000000000005100 | Instance: E55BB2E14A3D905C42BEC8AAB9003028 (DESKTOP-E41IK6R-42588).
[2025.05.23-15.53.36:443][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.23-15.53.36:472][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.23-15.53.36:472][  0]LogNNERuntimeORT: 0: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.23-15.53.36:472][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.23-15.53.36:472][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.23-15.53.36:479][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.23-15.53.36:479][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.23-15.53.36:479][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:54715'.
[2025.05.23-15.53.36:481][  0]LogUdpMessaging: Display: Added local interface '192.168.31.37' to multicast group '230.0.0.1:6666'
[2025.05.23-15.53.36:481][  0]LogUdpMessaging: Display: Added local interface '172.27.208.1' to multicast group '230.0.0.1:6666'
[2025.05.23-15.53.36:481][  0]LogUdpMessaging: Display: Added local interface '172.25.128.1' to multicast group '230.0.0.1:6666'
[2025.05.23-15.53.36:537][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.23-15.53.36:565][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.23-15.53.36:565][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.23-15.53.36:605][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.23-15.53.36:615][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.23-15.53.36:615][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.23-15.53.36:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.23-15.53.36:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.23-15.53.36:669][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.23-15.53.36:669][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.23-15.53.36:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.23-15.53.36:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.23-15.53.36:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.23-15.53.36:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.23-15.53.36:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.23-15.53.36:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.23-15.53.36:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.23-15.53.36:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.23-15.53.36:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.23-15.53.36:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.23-15.53.36:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.23-15.53.36:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.23-15.53.36:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.23-15.53.36:674][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.23-15.53.36:674][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.23-15.53.36:674][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.23-15.53.36:675][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.23-15.53.36:675][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.23-15.53.36:675][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.23-15.53.36:675][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.23-15.53.36:675][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.23-15.53.36:675][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.23-15.53.36:676][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.23-15.53.36:676][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.23-15.53.36:754][  0]SourceControl: Revision control is disabled
[2025.05.23-15.53.36:763][  0]SourceControl: Revision control is disabled
[2025.05.23-15.53.36:780][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.43ms
[2025.05.23-15.53.36:800][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.40ms
[2025.05.23-15.53.37:022][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.23-15.53.37:433][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.23-15.53.37:499][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.23-15.53.43:209][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.53.43:222][  0]LogSkeletalMesh: Built Skeletal Mesh [5.79s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.23-15.53.43:263][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.23-15.53.43:263][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.23-15.53.43:263][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.23-15.53.43:263][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.23-15.53.43:263][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.23-15.53.43:263][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.23-15.53.43:271][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.23-15.53.43:271][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.23-15.53.43:307][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.23-15.53.43:321][  0]LogCollectionManager: Loaded 0 collections in 0.000665 seconds
[2025.05.23-15.53.43:322][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.23-15.53.43:322][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.23-15.53.43:322][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.23-15.53.43:363][  0]LogBlenderLink: Waiting for client connection...
[2025.05.23-15.53.43:378][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.23-15.53.43:378][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.23-15.53.43:378][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.23-15.53.43:378][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.23-15.53.43:378][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.23-15.53.43:378][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.23-15.53.43:383][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.23-15.53.43:383][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.23-15.53.43:402][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-23T15:53:43.401Z using C
[2025.05.23-15.53.43:402][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.23-15.53.43:402][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.23-15.53.43:402][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.23-15.53.43:406][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.23-15.53.43:406][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.23-15.53.43:406][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.23-15.53.43:406][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000042
[2025.05.23-15.53.43:406][  0]LogFab: Display: Logging in using persist
[2025.05.23-15.53.43:407][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.23-15.53.43:433][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.23-15.53.43:433][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.23-15.53.43:444][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.23-15.53.43:444][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.23-15.53.43:549][  0]LogEngine: Initializing Engine...
[2025.05.23-15.53.43:551][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.23-15.53.43:551][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.23-15.53.43:617][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.23-15.53.43:627][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.23-15.53.43:635][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.23-15.53.43:635][  0]LogInit: Texture streaming: Enabled
[2025.05.23-15.53.43:641][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.23-15.53.43:652][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.23-15.53.43:656][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.23-15.53.43:656][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.23-15.53.43:656][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.23-15.53.43:656][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.23-15.53.43:656][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.23-15.53.43:656][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.23-15.53.43:656][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.23-15.53.43:656][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.23-15.53.43:656][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.23-15.53.43:659][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.23-15.53.43:706][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.23-15.53.43:706][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.23-15.53.43:706][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.23-15.53.43:706][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.23-15.53.43:707][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.23-15.53.43:707][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.23-15.53.43:709][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.23-15.53.43:709][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.23-15.53.43:709][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.23-15.53.43:709][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.23-15.53.43:709][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.23-15.53.43:714][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.23-15.53.43:717][  0]LogInit: Undo buffer set to 256 MB
[2025.05.23-15.53.43:717][  0]LogInit: Transaction tracking system initialized
[2025.05.23-15.53.43:726][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.23-15.53.43:760][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.45ms
[2025.05.23-15.53.43:761][  0]LocalizationService: Localization service is disabled
[2025.05.23-15.53.43:769][  0]LogTimingProfiler: Initialize
[2025.05.23-15.53.43:769][  0]LogTimingProfiler: OnSessionChanged
[2025.05.23-15.53.43:770][  0]LoadingProfiler: Initialize
[2025.05.23-15.53.43:770][  0]LoadingProfiler: OnSessionChanged
[2025.05.23-15.53.43:770][  0]LogNetworkingProfiler: Initialize
[2025.05.23-15.53.43:770][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.23-15.53.43:770][  0]LogMemoryProfiler: Initialize
[2025.05.23-15.53.43:770][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.23-15.53.43:881][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.23-15.53.43:889][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.23-15.53.43:916][  0]LogPython: Using Python 3.11.8
[2025.05.23-15.53.44:812][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.23-15.53.44:822][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.23-15.53.44:847][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.23-15.53.44:897][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.23-15.53.44:897][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.23-15.53.44:916][  0]LogEditorDataStorage: Initializing
[2025.05.23-15.53.44:916][  0]LogEditorDataStorage: Initialized
[2025.05.23-15.53.44:917][  0]LogWindows: Attached monitors:
[2025.05.23-15.53.44:917][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.23-15.53.44:917][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY2' [PRIMARY]
[2025.05.23-15.53.44:917][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY3'
[2025.05.23-15.53.44:917][  0]LogWindows: Found 3 attached monitors.
[2025.05.23-15.53.44:917][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.23-15.53.44:927][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.23-15.53.44:929][  0]SourceControl: Revision control is disabled
[2025.05.23-15.53.44:929][  0]LogUnrealEdMisc: Loading editor; pre map load, took 12.634
[2025.05.23-15.53.44:930][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.23-15.53.44:931][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.23-15.53.44:931][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.23-15.53.44:945][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.23-15.53.44:972][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.23-15.53.44:973][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.52ms
[2025.05.23-15.53.44:979][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.23-15.53.44:979][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.23-15.53.44:980][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.23-15.53.44:980][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.23-15.53.44:980][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.23-15.53.45:707][  0]LogAssetRegistry: Display: Asset registry cache written as 43.8 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.23-15.53.47:740][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.23-15.53.47:744][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.23-15.53.47:746][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.23-15.53.47:747][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.23-15.53.47:747][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.23-15.53.47:747][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.23-15.53.47:748][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.23-15.53.49:660][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.23-15.53.49:701][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.23-15.53.50:071][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.53.50:074][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.23-15.53.50:085][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.23-15.53.50:085][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.23-15.53.50:443][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.53.50:445][  0]LogSkeletalMesh: Built Skeletal Mesh [0.36s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.23-15.53.51:494][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.53.51:498][  0]LogSkeletalMesh: Built Skeletal Mesh [1.41s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.23-15.53.51:661][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.23-15.53.51:863][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.53.51:866][  0]LogSkeletalMesh: Built Skeletal Mesh [0.21s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.23-15.53.51:868][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.23-15.53.52:232][  0]LogWorldPartition: Display: WorldPartition initialize took 7.25 sec
[2025.05.23-15.53.52:305][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.23-15.53.56:749][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.53.56:761][  0]LogSkeletalMesh: Built Skeletal Mesh [4.89s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.23-15.53.57:375][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.23-15.53.57:585][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.09ms
[2025.05.23-15.53.57:585][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.23-15.53.57:587][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.61ms to complete.
[2025.05.23-15.53.57:596][  0]LogUnrealEdMisc: Total Editor Startup Time, took 25.301
[2025.05.23-15.53.57:778][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.23-15.53.57:857][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.23-15.53.57:906][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.23-15.53.57:954][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.23-15.53.57:994][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.23-15.53.58:024][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:024][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.23-15.53.58:024][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:024][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.23-15.53.58:024][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:024][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.23-15.53.58:025][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:025][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.23-15.53.58:025][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:025][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.23-15.53.58:025][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:025][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.23-15.53.58:026][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:026][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.23-15.53.58:026][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:026][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.23-15.53.58:026][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:026][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.23-15.53.58:026][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.23-15.53.58:026][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.23-15.53.58:062][  0]LogSlate: Took 0.000053 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.23-15.53.58:696][  0]LogSlate: External Image Picker: DecompressImage failed
[2025.05.23-15.53.58:834][  0]LogStall: Startup...
[2025.05.23-15.53.58:836][  0]LogStall: Startup complete.
[2025.05.23-15.53.58:841][  0]LogLoad: (Engine Initialization) Total time: 26.55 seconds
[2025.05.23-15.53.58:981][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.23-15.53.58:998][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.23-15.53.59:000][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.23-15.53.59:000][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.23-15.53.59:000][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.23-15.53.59:062][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.23-15.53.59:063][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.23-15.53.59:064][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.23-15.53.59:064][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.23-15.53.59:064][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.23-15.53.59:119][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.23-15.53.59:120][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.23-15.53.59:350][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.23-15.53.59:351][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.23-15.53.59:452][  0]LogSlate: Took 0.000077 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.23-15.53.59:454][  0]LogSlate: Took 0.000061 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.23-15.53.59:456][  0]LogSlate: Took 0.000052 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.23-15.53.59:504][  0]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.23-15.53.59:583][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.23-15.53.59:590][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.23-15.53.59:590][  0]LogFab: Display: Logging in using exchange code
[2025.05.23-15.53.59:590][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.23-15.53.59:590][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.23-15.53.59:591][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.23-15.53.59:656][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.23-15.53.59:670][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 79.186 ms
[2025.05.23-15.53.59:670][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.23-15.53.59:681][  1]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 18.83 ms. Compile time 14.85 ms, link time 3.72 ms.
[2025.05.23-15.53.59:842][  1]LogAssetRegistry: AssetRegistryGather time 0.0720s: AssetDataDiscovery 0.0136s, AssetDataGather 0.0100s, StoreResults 0.0484s. Wall time 24.8030s.
	NumCachedDirectories 0. NumUncachedDirectories 1855. NumCachedFiles 7965. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.23-15.53.59:888][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.23-15.53.59:888][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.23-15.53.59:983][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.23-15.53.59:998][  2]LogSourceControl: Uncontrolled asset enumeration finished in 0.110776 seconds (Found 7941 uncontrolled assets)
[2025.05.23-15.54.00:476][  3]LogSlate: External Image Picker: DecompressImage failed
[2025.05.23-15.54.00:637][  4]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 17.166109
[2025.05.23-15.54.00:638][  4]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.23-15.54.00:638][  4]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17.230104
[2025.05.23-15.54.01:099][ 18]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.23-15.54.01:586][ 66]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 18.168108
[2025.05.23-15.54.01:587][ 66]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.23-15.54.01:587][ 66]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18.168108, Update Interval: 350.141296
[2025.05.23-15.54.09:121][535]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.54.14:671][616]LogStreaming: Display: FlushAsyncLoading(512): 1 QueuedPackages, 0 AsyncPackages
[2025.05.23-15.54.14:704][616]LogInterchangeEngine: Display: Interchange start importing source [C:/Users/<USER>/Desktop/Metahuman.fbx]
[2025.05.23-15.55.34:149][616]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.23-15.56.34:384][616]LogSlate: Took 0.000060 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.23-15.56.34:414][616]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.05.23-15.56.34:415][616]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.05.23-15.56.34:421][616]LogSkeletalMesh: Building Skeletal Mesh SKM_Face_Preview...
[2025.05.23-15.56.34:432][616]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.23-15.56.34:634][616]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.23-15.56.34:638][616]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
[2025.05.23-15.56.38:657][616]LogSlate: Window 'Import Content' being destroyed
[2025.05.23-15.56.38:681][616]LogInterchangeEngine: [Pending] Importing
[2025.05.23-15.56.38:835][617]LogStreaming: Display: FlushAsyncLoading(518): 1 QueuedPackages, 0 AsyncPackages
[2025.05.23-15.56.38:837][617]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.56.40:638][655]LogStreaming: Display: FlushAsyncLoading(519): 1 QueuedPackages, 0 AsyncPackages
[2025.05.23-15.56.41:200][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Ewiden_INL' found, renamed to '_Braise_Ewiden_INL_1'.
[2025.05.23-15.56.41:200][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Ewiden_INR' found, renamed to '_Braise_Ewiden_INR_1'.
[2025.05.23-15.56.41:200][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Ewiden_OUTL' found, renamed to '_Braise_Ewiden_OUTL_1'.
[2025.05.23-15.56.41:200][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Ewiden_OUTR' found, renamed to '_Braise_Ewiden_OUTR_1'.
[2025.05.23-15.56.41:200][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Eblink_INL' found, renamed to '_Braise_Eblink_INL_1'.
[2025.05.23-15.56.41:200][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Eblink_INR' found, renamed to '_Braise_Eblink_INR_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Eblink_OUTL' found, renamed to '_Braise_Eblink_OUTL_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_Eblink_OUTR' found, renamed to '_Braise_Eblink_OUTR_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_ElookDown_INL' found, renamed to '_Braise_ElookDown_INL_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_ElookDown_INR' found, renamed to '_Braise_ElookDown_INR_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_ElookDown_OUTL' found, renamed to '_Braise_ElookDown_OUTL_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_Braise_ElookDown_OUTR' found, renamed to '_Braise_ElookDown_OUTR_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_blink_L' found, renamed to '_eye_blink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_blink_R' found, renamed to '_eye_blink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElidPress_Eblink_L' found, renamed to '_ElidPress_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElidPress_Eblink_R' found, renamed to '_ElidPress_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_widen_L' found, renamed to '_eye_widen_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_widen_R' found, renamed to '_eye_widen_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_squintInner_L' found, renamed to '_eye_squintInner_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_squintInner_R' found, renamed to '_eye_squintInner_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_cheekRaise_L' found, renamed to '_eye_cheekRaise_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_cheekRaise_R' found, renamed to '_eye_cheekRaise_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_EsquintInner_L' found, renamed to '_EcheekRaise_EsquintInner_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_EsquintInner_R' found, renamed to '_EcheekRaise_EsquintInner_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EsquintInner_Eblink_L' found, renamed to '_EsquintInner_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EsquintInner_Eblink_R' found, renamed to '_EsquintInner_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_Eblink_L' found, renamed to '_EcheekRaise_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_Eblink_R' found, renamed to '_EcheekRaise_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_EsquintInner_Eblink_L' found, renamed to '_EcheekRaise_EsquintInner_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_EsquintInner_Eblink_R' found, renamed to '_EcheekRaise_EsquintInner_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EfaceScrunch_Eblink_L' found, renamed to '_EfaceScrunch_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EfaceScrunch_Eblink_R' found, renamed to '_EfaceScrunch_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_upperLidUp_L' found, renamed to '_eye_upperLidUp_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_upperLidUp_R' found, renamed to '_eye_upperLidUp_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_relax_L' found, renamed to '_eye_relax_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_relax_R' found, renamed to '_eye_relax_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lowerLidUp_L' found, renamed to '_eye_lowerLidUp_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lowerLidUp_R' found, renamed to '_eye_lowerLidUp_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lowerLidDown_L' found, renamed to '_eye_lowerLidDown_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lowerLidDown_R' found, renamed to '_eye_lowerLidDown_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookUp_L' found, renamed to '_eye_lookUp_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookUp_R' found, renamed to '_eye_lookUp_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookDown_L' found, renamed to '_eye_lookDown_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookDown_R' found, renamed to '_eye_lookDown_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookLeft_L' found, renamed to '_eye_lookLeft_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookLeft_R' found, renamed to '_eye_lookLeft_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookRight_L' found, renamed to '_eye_lookRight_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_eye_lookRight_R' found, renamed to '_eye_lookRight_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookLeft_L' found, renamed to '_ElookUp_ElookLeft_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookLeft_R' found, renamed to '_ElookUp_ElookLeft_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookRight_L' found, renamed to '_ElookUp_ElookRight_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookRight_R' found, renamed to '_ElookUp_ElookRight_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookLeft_L' found, renamed to '_ElookDown_ElookLeft_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookLeft_R' found, renamed to '_ElookDown_ElookLeft_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookRight_L' found, renamed to '_ElookDown_ElookRight_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookRight_R' found, renamed to '_ElookDown_ElookRight_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_Eblink_L' found, renamed to '_ElookUp_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_Eblink_R' found, renamed to '_ElookUp_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_Eblink_L' found, renamed to '_ElookDown_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_Eblink_R' found, renamed to '_ElookDown_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookLeft_Eblink_L' found, renamed to '_ElookLeft_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookLeft_Eblink_R' found, renamed to '_ElookLeft_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookRight_Eblink_L' found, renamed to '_ElookRight_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookRight_Eblink_R' found, renamed to '_ElookRight_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookLeft_Eblink_L' found, renamed to '_ElookUp_ElookLeft_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookLeft_Eblink_R' found, renamed to '_ElookUp_ElookLeft_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookRight_Eblink_L' found, renamed to '_ElookUp_ElookRight_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookUp_ElookRight_Eblink_R' found, renamed to '_ElookUp_ElookRight_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookLeft_Eblink_L' found, renamed to '_ElookDown_ElookLeft_Eblink_L_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookLeft_Eblink_R' found, renamed to '_ElookDown_ElookLeft_Eblink_R_1'.
[2025.05.23-15.56.41:201][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookRight_Eblink_L' found, renamed to '_ElookDown_ElookRight_Eblink_L_1'.
[2025.05.23-15.56.41:202][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_ElookRight_Eblink_R' found, renamed to '_ElookDown_ElookRight_Eblink_R_1'.
[2025.05.23-15.56.41:202][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_Ewiden_L' found, renamed to '_ElookDown_Ewiden_L_1'.
[2025.05.23-15.56.41:202][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_ElookDown_Ewiden_R' found, renamed to '_ElookDown_Ewiden_R_1'.
[2025.05.23-15.56.41:202][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_NSwrinkle_L' found, renamed to '_EcheekRaise_NSwrinkle_L_1'.
[2025.05.23-15.56.41:202][705]LogSkeletalMeshLODImporterData: Warning: [AssetLog] : Duplicate morph target '_EcheekRaise_NSwrinkle_R' found, renamed to '_EcheekRaise_NSwrinkle_R_1'.
[2025.05.23-15.56.42:692][829]LogAnimationCompression: Display: Building compressed animation data for AnimSequence /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim (Required Memory Estimate: 5.61 MB)
[2025.05.23-15.56.42:740][829]LogAnimationCompression: Display: Storing compressed animation data for /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim, at AnimationSequence/b01fd1cb07b9ab6af9a0442ece1f05f3fb14a873
[2025.05.23-15.56.42:864][829]LogAnimationCompression: Display: Building compressed animation data for AnimSequence /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim (Required Memory Estimate: 100.51 MB)
[2025.05.23-15.56.42:875][830]LogSkeletalMesh: Building Skeletal Mesh Metahuman...
[2025.05.23-15.56.42:896][831]LogSlate: Took 0.000199 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.23-15.56.48:792][261]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.56.49:517][317]LogSkeletalMesh: Built Skeletal Mesh [6.64s] /Game/MetaHumans/Test2/Metahuman.Metahuman
[2025.05.23-15.56.49:571][319]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Metahuman) ...
[2025.05.23-15.56.49:615][319]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.23-15.56.49:624][319]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.23-15.56.49:624][319]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.23-15.56.49:624][319]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.23-15.56.49:631][319]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.23-15.56.49:643][319]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.23-15.56.49:643][319]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.23-15.56.49:752][320]LogUObjectHash: Compacting FUObjectHashTables data took   0.89ms
[2025.05.23-15.56.50:104][320]LogInterchangeEngine: Display: Interchange import completed [C:/Users/<USER>/Desktop/Metahuman.fbx]
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'cartilage_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'eyeEdge_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'eyeLeft_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'eyeRight_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'eyelashes_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'eyeshell_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'head_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'saliva_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:104][320]Interchange: Warning: [C:/Users/<USER>/Desktop/Metahuman.fbx : 'Metahuman', SkeletalMesh] No smoothing group information was found for this mesh 'teeth_lod0_mesh' in the FBX file. Please make sure to enable the 'Export Smoothing Groups' option in the FBX Exporter before exporting the file.
[2025.05.23-15.56.50:140][320]LogInterchangeEngine: [Pending] Importing - Operation completed.
[2025.05.23-15.56.50:140][320]LogInterchangeEngine: [Success] Import Done
[2025.05.23-15.56.50:146][321]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.23-15.56.50:202][321]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.23-15.56.50:245][322]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.23-15.56.53:853][653]LogAnimationCompression: Display: Storing compressed animation data for /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim, at AnimationSequence/2e10f46578fd9eee34352479edbd01831d82e885
[2025.05.23-15.56.54:990][744]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Test2/Metahuman.Metahuman
[2025.05.23-15.56.54:995][744]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.23-15.56.55:002][744]LogStreaming: Display: FlushAsyncLoading(520): 1 QueuedPackages, 0 AsyncPackages
[2025.05.23-15.56.55:021][744]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_6:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.23-15.56.58:804][987]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.57.08:806][773]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.57.18:816][561]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.57.28:864][356]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.57.32:423][630]LogSlate: Window 'Metahuman' being destroyed
[2025.05.23-15.57.32:429][630]LogSlate: Window 'Metahuman' being destroyed
[2025.05.23-15.57.32:445][630]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.23-15.57.32:445][630]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.23-15.57.32:506][630]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.23-15.57.38:925][ 35]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.57.48:926][ 65]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.57.58:926][ 95]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.58.08:926][125]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.58.18:926][155]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.58.28:926][185]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.58.38:926][215]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.58.48:928][245]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.58.58:929][275]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.59.08:930][305]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.59.18:932][335]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.59.28:935][365]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.59.38:936][395]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.59.48:936][425]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-15.59.52:269][435]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 368.863586
[2025.05.23-15.59.53:270][438]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-15.59.53:270][438]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 369.530914, Update Interval: 307.727295
[2025.05.23-15.59.58:938][455]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.00.08:939][485]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.00.18:941][515]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.00.28:942][545]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.00.38:942][575]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.00.48:943][605]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.00.58:942][635]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.01.08:943][665]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.01.18:942][695]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.01.28:946][725]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.01.38:944][755]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.01.48:946][785]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.01.58:946][815]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.02.08:946][845]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.02.18:947][875]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.02.28:948][905]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.02.38:949][935]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.02.48:949][965]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.02.58:949][995]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.08:950][ 25]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.18:951][ 55]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.28:952][ 85]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.38:952][115]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.48:953][145]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.58:953][175]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.03.59:331][176]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.23-16.03.59:334][176]LogFileHelpers: Editor autosave (incl. external actors) for '/Game/MetaHumans/Test/TestLevel' took 0.015
[2025.05.23-16.03.59:334][176]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton] ([1] browsable assets)...
[2025.05.23-16.03.59:334][176]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.23-16.03.59:456][176]OBJ SavePackage:     Rendered thumbnail for [Skeleton /Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton]
[2025.05.23-16.03.59:456][176]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton]
[2025.05.23-16.03.59:495][176]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton_Auto1
[2025.05.23-16.03.59:496][176]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/Face_Archetype_Skeleton_Auto15628C7704C759F4EC721768002ECCEDE.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton_Auto1.uasset'
[2025.05.23-16.03.59:497][176]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim] ([1] browsable assets)...
[2025.05.23-16.03.59:497][176]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.23-16.03.59:520][176]OBJ SavePackage:     Rendered thumbnail for [AnimSequence /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim]
[2025.05.23-16.03.59:520][176]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim]
[2025.05.23-16.03.59:706][176]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim_Auto1
[2025.05.23-16.03.59:706][176]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/mh_arkit_mapping_anim_Auto17B96DB0F4F71DC018CDA90AC149F806F.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim_Auto1.uasset'
[2025.05.23-16.03.59:708][176]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim] ([1] browsable assets)...
[2025.05.23-16.03.59:722][176]OBJ SavePackage:     Rendered thumbnail for [AnimSequence /Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim.neckCorr_m_med_nrw_RBFSolver_anim]
[2025.05.23-16.03.59:722][176]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim]
[2025.05.23-16.03.59:786][176]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim_Auto1
[2025.05.23-16.03.59:787][176]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/neckCorr_m_med_nrw_RBFSolver_ani9FC9249D4919491564F3188DC3F6CC3C.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Common/Face/neckPoses/neckCorr_m_med_nrw_RBFSolver_anim_Auto1.uasset'
[2025.05.23-16.03.59:788][176]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Test2/Metahuman_PhysicsAsset] ([1] browsable assets)...
[2025.05.23-16.03.59:793][176]OBJ SavePackage:     Rendered thumbnail for [PhysicsAsset /Game/MetaHumans/Test2/Metahuman_PhysicsAsset.Metahuman_PhysicsAsset]
[2025.05.23-16.03.59:793][176]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test2/Metahuman_PhysicsAsset]
[2025.05.23-16.03.59:794][176]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Test2/Metahuman_PhysicsAsset_Auto1
[2025.05.23-16.03.59:795][176]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/Metahuman_PhysicsAsset_Auto1CE09122940ED83679E0037BE093EB9C5.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test2/Metahuman_PhysicsAsset_Auto1.uasset'
[2025.05.23-16.03.59:795][176]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Test2/Metahuman] ([1] browsable assets)...
[2025.05.23-16.03.59:801][176]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/MetaHumans/Test2/Metahuman.Metahuman]
[2025.05.23-16.03.59:801][176]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test2/Metahuman]
[2025.05.23-16.03.59:801][176]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Test2/Metahuman" FILE="H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test2/Metahuman_Auto1.uasset" SILENT=false AUTOSAVING=true
[2025.05.23-16.04.00:103][176]LogSavePackage: Moving output files for package: /Temp/Autosaves/Game/MetaHumans/Test2/Metahuman_Auto1
[2025.05.23-16.04.00:103][176]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/Metahuman_Auto102189F0F4B6217A5FB8F10B56F4C830E.tmp' to 'H:/Plugins/BlenderLinkProject/Saved/Autosaves/Game/MetaHumans/Test2/Metahuman_Auto1.uasset'
[2025.05.23-16.04.00:106][176]LogFileHelpers: Auto-saving content packages took 0.773
[2025.05.23-16.04.09:132][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.04.19:132][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.04.29:132][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.04.39:134][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.04.49:136][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.04.59:136][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.05.04:470][370]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 681.065552
[2025.05.23-16.05.05:470][373]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.05.05:470][373]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 681.732422, Update Interval: 339.907227
[2025.05.23-16.05.09:136][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.05.19:138][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.05.29:138][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.05.39:138][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.05.49:139][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.05.59:140][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.06.09:140][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.06.19:140][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.06.29:141][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.06.39:141][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.06.49:141][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.06.59:142][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.07.09:142][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.07.19:142][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.07.29:143][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.07.39:143][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.07.49:143][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.07.59:143][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.08.09:144][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.08.19:144][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.08.29:144][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.08.39:145][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.08.49:144][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.08.59:145][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.09.09:145][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.09.19:146][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.09.29:146][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.09.39:145][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.09.49:146][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.09.59:146][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.10.09:147][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.10.19:148][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.10.29:148][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.10.39:148][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.10.49:150][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.10.52:816][415]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1029.411499
[2025.05.23-16.10.53:816][418]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.10.53:816][418]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1030.078003, Update Interval: 328.277832
[2025.05.23-16.10.59:150][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.11.09:150][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.11.19:150][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.11.29:150][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.11.39:153][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.11.49:157][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.11.59:157][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.12.09:159][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.12.19:159][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.12.29:159][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.12.39:161][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.12.49:161][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.12.59:161][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.13.09:161][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.13.19:161][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.13.29:162][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.13.39:161][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.13.49:162][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.13.59:163][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.14.09:163][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.14.19:165][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.14.29:164][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.14.39:164][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.14.49:164][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.14.59:164][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.15.09:165][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.15.19:165][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.15.29:166][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.15.39:166][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.15.49:167][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.15.59:167][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.16.09:169][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.16.19:168][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.16.29:169][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.16.33:503][437]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1370.098633
[2025.05.23-16.16.35:170][442]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.16.35:170][442]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1371.432373, Update Interval: 335.455811
[2025.05.23-16.16.39:170][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.16.49:170][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.16.59:171][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.17.09:171][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.17.19:172][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.17.29:173][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.17.39:173][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.17.49:173][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.17.59:174][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.18.09:174][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.18.19:174][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.18.29:174][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.18.39:175][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.18.49:174][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.18.59:174][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.19.09:175][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.19.19:175][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.19.29:176][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.19.39:176][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.19.49:176][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.19.59:177][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.20.09:177][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.20.19:178][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.20.29:179][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.20.39:178][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.20.49:179][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.20.59:180][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.21.09:181][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.21.19:181][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.21.29:181][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.21.39:182][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.21.49:182][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.21.59:182][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.22.09:183][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.22.19:182][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.22.29:182][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.22.37:516][529]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1734.108887
[2025.05.23-16.22.38:516][532]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.22.38:516][532]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1734.775757, Update Interval: 354.048889
[2025.05.23-16.22.39:182][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.22.49:184][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.22.59:184][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.23.09:184][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.23.19:185][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.23.29:186][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.23.39:187][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.23.49:187][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.23.59:189][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.24.09:188][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.24.19:189][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.24.29:189][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.24.39:189][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.24.49:189][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.24.59:190][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.25.09:191][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.25.19:191][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.25.29:191][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.25.39:191][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.25.49:192][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.25.59:192][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.26.09:194][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.26.19:196][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.26.29:196][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.26.39:196][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.26.49:197][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.26.59:198][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.27.09:199][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.27.19:202][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.27.29:202][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.27.39:202][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.27.49:202][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.27.59:204][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.28.09:204][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.28.19:206][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.28.29:204][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.28.39:205][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.28.49:206][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.28.59:206][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.29.09:207][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.29.19:208][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.29.20:208][737]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2136.799561
[2025.05.23-16.29.21:208][740]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.29.21:208][740]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2137.466064, Update Interval: 354.164246
[2025.05.23-16.29.29:208][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.29.39:207][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.29.49:208][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.29.59:208][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.30.09:209][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.30.19:209][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.30.29:209][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.30.39:211][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.30.49:210][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.30.59:212][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.31.09:212][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.31.19:212][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.31.29:213][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.31.39:213][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.31.49:213][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.31.59:214][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.32.09:215][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.32.19:215][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.32.29:215][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.32.39:217][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.32.49:217][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.32.59:218][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.33.09:218][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.33.19:219][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.33.29:220][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.33.39:220][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.33.49:220][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.33.59:221][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.34.09:223][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.34.19:224][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.34.29:225][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.34.39:225][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.34.49:226][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.34.59:226][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.35.09:226][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.35.19:228][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.35.29:228][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.35.39:228][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.35.49:229][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.35.59:229][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.36.09:231][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.36.12:898][975]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2549.488037
[2025.05.23-16.36.13:898][978]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.36.13:898][978]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2550.155029, Update Interval: 351.435883
[2025.05.23-16.36.19:231][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.36.29:231][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.36.39:232][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.36.49:232][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.36.59:235][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.37.09:235][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.37.19:234][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.37.29:235][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.37.39:235][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.37.49:235][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.37.59:235][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.38.09:236][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.38.19:235][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.38.29:236][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.38.39:236][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.38.49:237][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.38.59:238][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.39.09:239][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.39.19:242][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.39.29:242][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.39.39:243][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.39.49:243][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.39.59:243][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.40.09:245][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.40.19:246][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.40.29:246][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.40.39:248][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.40.49:248][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.40.59:248][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.41.09:248][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.41.19:249][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.41.29:249][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.41.39:249][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.41.49:249][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.41.59:250][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.42.09:250][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.42.19:251][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.42.29:252][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.42.39:253][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.42.49:253][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.42.53:254][176]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2949.843994
[2025.05.23-16.42.54:253][179]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.42.54:253][179]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2950.511230, Update Interval: 303.438812
[2025.05.23-16.42.59:254][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.43.09:254][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.43.19:254][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.43.29:254][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.43.39:255][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.43.49:255][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.43.59:257][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.44.09:256][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.44.19:257][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.44.29:259][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.44.39:259][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.44.49:259][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.44.59:259][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.45.09:258][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.45.19:260][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.45.29:260][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.45.39:261][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.45.49:262][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.45.59:262][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.46.09:263][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.46.19:264][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.46.29:265][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.46.39:266][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.46.49:267][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.46.59:267][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.47.09:267][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.47.19:269][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.47.29:269][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.47.39:268][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.47.49:269][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.47.59:269][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.48.09:270][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.48.19:273][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.48.29:272][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.48.39:272][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.48.49:273][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.48.51:606][251]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3308.193604
[2025.05.23-16.48.52:607][254]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.48.52:607][254]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3308.860107, Update Interval: 355.504639
[2025.05.23-16.48.59:273][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.49.09:274][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.49.19:275][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.49.29:276][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.49.39:276][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.49.49:276][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.49.59:276][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.50.09:278][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.50.19:281][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.50.29:278][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.50.39:278][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.50.49:278][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.50.59:279][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.51.09:279][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.51.19:280][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.51.29:280][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.51.39:280][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.51.49:281][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.51.59:282][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.52.09:282][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.52.19:284][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.52.29:284][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.52.39:285][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.52.49:286][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.52.59:286][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.53.09:287][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.53.19:287][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.53.29:288][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.53.39:288][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.53.49:289][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.53.59:288][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.54.09:290][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.54.19:290][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.54.29:290][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.54.39:291][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.54.49:292][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.54.59:292][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.55.09:292][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.55.19:293][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.55.29:293][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.55.34:150][459]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.23-16.55.35:627][463]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3712.219727
[2025.05.23-16.55.36:626][466]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-16.55.36:626][466]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3712.886230, Update Interval: 334.624481
[2025.05.23-16.55.39:294][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.55.49:294][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.55.59:294][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.56.09:294][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.56.19:294][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.56.29:296][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.56.39:296][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.56.49:297][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.56.59:297][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.57.09:298][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.57.19:298][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.57.29:298][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.57.39:299][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.57.49:299][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.57.59:299][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.58.09:299][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.58.19:299][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.58.29:300][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.58.39:301][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.58.49:301][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.58.59:303][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.59.09:304][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.59.19:305][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.59.29:308][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.59.39:308][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.59.49:308][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-16.59.59:309][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.00.09:309][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.00.19:309][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.00.29:310][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.00.39:311][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.00.49:310][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.00.59:312][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.01.09:312][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.01.19:314][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.01.29:313][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.01.39:313][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.01.44:314][569]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4080.902344
[2025.05.23-17.01.45:314][572]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.01.45:314][572]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4081.569092, Update Interval: 310.208435
[2025.05.23-17.01.49:315][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.01.59:314][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.02.09:316][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.02.19:315][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.02.29:316][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.02.39:316][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.02.49:316][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.02.59:317][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.03.09:319][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.03.19:320][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.03.29:319][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.03.39:320][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.03.49:320][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.03.59:320][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.04.09:321][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.04.19:323][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.04.29:322][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.04.39:324][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.04.49:324][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.04.59:326][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.05.09:326][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.05.19:327][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.05.29:327][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.05.39:328][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.05.49:329][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.05.59:329][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.06.09:330][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.06.19:331][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.06.29:331][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.06.39:332][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.06.49:332][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.06.59:333][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.07.09:334][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.07.19:334][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.07.29:335][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.07.39:336][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.07.49:336][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.07.51:337][670]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4447.944824
[2025.05.23-17.07.52:337][673]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.07.52:337][673]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4448.612305, Update Interval: 320.003052
[2025.05.23-17.07.59:337][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.08.09:338][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.08.19:338][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.08.29:339][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.08.39:341][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.08.49:341][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.08.59:341][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.09.09:341][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.09.19:342][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.09.29:343][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.09.39:343][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.09.49:343][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.09.59:344][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.10.09:344][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.10.19:344][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.10.29:344][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.10.39:344][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.10.49:345][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.10.59:346][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.11.09:346][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.11.19:347][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.11.29:349][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.11.39:348][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.11.49:348][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.11.59:348][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.12.09:349][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.12.19:350][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.12.29:351][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.12.39:352][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.12.49:352][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.12.59:354][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.13.09:353][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.13.19:354][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.13.29:355][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.13.39:355][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.13.49:356][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.13.52:356][753]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4808.976074
[2025.05.23-17.13.53:356][756]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.13.53:356][756]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4809.642578, Update Interval: 356.389038
[2025.05.23-17.13.59:358][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.14.09:358][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.14.19:360][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.14.29:360][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.14.39:361][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.14.49:363][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.14.59:363][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.15.09:363][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.15.19:365][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.15.29:365][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.15.39:365][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.15.49:366][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.15.59:366][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.16.09:366][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.16.19:366][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.16.29:367][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.16.39:368][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.16.49:369][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.16.59:370][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.17.09:371][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.17.19:371][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.17.29:372][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.17.39:372][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.17.49:372][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.17.59:372][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.18.09:373][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.18.19:373][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.18.29:374][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.18.39:375][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.18.49:377][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.18.59:377][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.19.09:378][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.19.19:379][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.19.29:379][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.19.39:380][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.19.49:380][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.19.59:380][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.20.09:380][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.20.19:380][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.20.24:047][928]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5200.676270
[2025.05.23-17.20.25:381][932]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.20.25:381][932]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5201.676270, Update Interval: 359.758301
[2025.05.23-17.20.29:381][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.20.39:381][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.20.49:381][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.20.59:381][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.21.09:383][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.21.19:383][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.21.29:383][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.21.39:384][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.21.49:384][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.21.59:385][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.22.09:386][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.22.19:387][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.22.29:389][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.22.39:389][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.22.49:389][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.22.59:390][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.23.09:391][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.23.19:393][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.23.29:393][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.23.39:393][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.23.49:394][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.23.59:394][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.24.09:394][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.24.19:395][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.24.29:396][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.24.39:397][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.24.49:398][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.24.59:398][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.25.09:398][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.25.19:401][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.25.29:401][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.25.39:401][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.25.49:402][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.25.59:403][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.26.09:403][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.26.19:404][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.26.29:405][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.26.39:406][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.26.49:406][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.26.59:406][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.27.08:740][142]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5605.384277
[2025.05.23-17.27.09:406][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.27.09:740][145]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.27.09:741][145]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5606.050781, Update Interval: 309.408234
[2025.05.23-17.27.19:408][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.27.29:408][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.27.39:408][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.27.49:408][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.27.59:412][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.28.09:411][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.28.19:413][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.28.29:413][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.28.39:414][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.28.49:414][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.28.59:414][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.29.09:415][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.29.19:414][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.29.29:415][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.29.39:417][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.29.49:417][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.29.59:418][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.30.09:418][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.30.19:419][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.30.29:421][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.30.39:420][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.30.49:420][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.30.59:420][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.31.09:420][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.31.19:420][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.31.29:421][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.31.39:422][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.31.49:424][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.31.59:426][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.09:428][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.19:428][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.29:429][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.39:429][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.49:431][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.59:432][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.32.59:766][195]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5956.409668
[2025.05.23-17.33.00:765][198]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.33.00:765][198]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5957.075684, Update Interval: 355.764648
[2025.05.23-17.33.09:432][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.33.19:431][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.33.29:432][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.33.39:434][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.33.49:434][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.33.59:435][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.34.09:436][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.34.19:436][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.34.29:437][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.34.39:438][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.34.49:439][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.34.59:439][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.35.09:439][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.35.19:440][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.35.29:440][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.35.39:441][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.35.49:441][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.35.59:442][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.36.09:443][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.36.19:443][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.36.29:443][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.36.39:443][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.36.49:443][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.36.59:444][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.37.09:444][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.37.19:445][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.37.29:445][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.37.39:446][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.37.49:446][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.37.59:446][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.38.09:448][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.38.19:449][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.38.29:448][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.38.39:450][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.38.49:451][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.38.59:451][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.39.09:452][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.39.19:453][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.39.29:118][363]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6345.781250
[2025.05.23-17.39.29:451][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.39.30:118][366]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.39.30:118][366]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6346.447754, Update Interval: 338.334290
[2025.05.23-17.39.39:452][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.39.49:453][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.39.59:456][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.40.09:457][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.40.19:456][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.40.29:456][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.40.39:458][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.40.49:459][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.40.59:459][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.41.09:459][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.41.19:459][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.41.29:459][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.41.39:460][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.41.49:460][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.41.59:462][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.42.09:462][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.42.19:462][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.42.29:464][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.42.39:464][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.42.49:463][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.42.59:465][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.43.09:465][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.43.19:466][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.43.29:466][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.43.39:468][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.43.49:467][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.43.59:467][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.44.09:468][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.44.19:469][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.44.29:469][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.44.39:470][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.44.49:471][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.44.59:472][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.45.09:472][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.45.19:472][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.45.29:474][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.45.39:474][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.45.39:808][475]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6716.481445
[2025.05.23-17.45.40:808][478]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.45.40:808][478]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6717.147949, Update Interval: 319.102142
[2025.05.23-17.45.49:475][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.45.59:476][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.46.09:476][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.46.19:478][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.46.29:478][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.46.39:478][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.46.49:478][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.46.59:480][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.47.09:480][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.47.19:480][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.47.29:481][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.47.39:482][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.47.49:483][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.47.59:483][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.48.09:484][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.48.19:484][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.48.29:484][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.48.39:485][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.48.49:486][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.48.59:486][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.49.09:487][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.49.19:489][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.49.29:489][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.49.39:490][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.49.49:490][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.49.59:492][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.50.09:492][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.50.19:492][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.50.29:492][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.50.39:492][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.50.49:493][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.50.59:494][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.51.09:495][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.51.19:496][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.51.29:496][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.51.39:496][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.51.49:498][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.51.51:498][590]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7088.200195
[2025.05.23-17.51.52:498][593]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.51.52:498][593]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7088.867188, Update Interval: 328.488403
[2025.05.23-17.51.59:499][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.52.09:500][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.52.19:500][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.52.29:500][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.52.39:502][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.52.49:503][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.52.59:503][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.53.09:504][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.53.19:506][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.53.29:508][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.53.39:509][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.53.49:511][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.53.59:511][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.54.09:512][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.54.19:512][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.54.29:512][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.54.39:513][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.54.49:514][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.54.59:514][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.55.09:515][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.55.19:515][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.55.29:516][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.55.34:150][258]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.23-17.55.39:516][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.55.49:517][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.55.59:519][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.56.09:519][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.56.19:520][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.56.29:520][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.56.39:521][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.56.49:522][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.56.59:523][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.57.09:522][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.57.19:524][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.57.29:526][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.57.39:525][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.57.49:526][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.57.56:860][686]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7453.579590
[2025.05.23-17.57.57:860][689]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-17.57.57:860][689]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7454.246094, Update Interval: 348.150879
[2025.05.23-17.57.59:526][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.58.09:528][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.58.19:529][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.58.29:529][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.58.39:529][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.58.49:529][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.58.59:529][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.59.09:530][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.59.19:529][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.59.29:530][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.59.39:530][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.59.49:531][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-17.59.59:531][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.00.09:534][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.00.19:537][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.00.29:537][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.00.39:537][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.00.49:539][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.00.59:539][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.01.09:539][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.01.19:539][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.01.29:539][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.01.39:539][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.01.49:540][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.01.59:540][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.02.09:541][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.02.19:542][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.02.29:542][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.02.39:543][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.02.49:544][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.02.59:545][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.03.09:545][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.03.19:546][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.03.29:546][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.03.39:547][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.03.49:548][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.03.59:548][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.04.09:549][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.04.19:549][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.04.29:549][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.04.37:551][888]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7854.301270
[2025.05.23-18.04.38:550][891]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.04.38:550][891]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7854.967773, Update Interval: 359.298676
[2025.05.23-18.04.39:550][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.04.49:551][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.04.59:552][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.05.09:554][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.05.19:553][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.05.29:554][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.05.39:555][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.05.49:556][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.05.59:557][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.06.09:556][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.06.19:558][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.06.29:558][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.06.39:559][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.06.49:560][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.06.59:560][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.07.09:562][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.07.19:563][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.07.29:565][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.07.39:565][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.07.49:566][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.07.59:566][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.08.09:566][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.08.19:566][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.08.29:566][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.08.39:567][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.08.49:567][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.08.59:568][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.09.09:569][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.09.19:570][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.09.29:572][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.09.39:572][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.09.49:572][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.09.59:573][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.10.09:573][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.10.19:573][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.10.29:575][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.10.39:576][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.10.49:576][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.10.59:577][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.11.09:577][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.11.18:578][ 91]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8255.328125
[2025.05.23-18.11.19:577][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.11.19:578][ 94]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.11.19:578][ 94]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8255.994141, Update Interval: 324.132202
[2025.05.23-18.11.29:578][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.11.39:578][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.11.49:580][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.11.59:580][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.12.09:581][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.12.19:581][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.12.29:581][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.12.39:583][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.12.49:582][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.12.59:583][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.13.09:583][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.13.19:583][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.13.29:583][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.13.39:582][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.13.49:584][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.13.59:583][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.14.09:585][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.14.19:585][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.14.29:586][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.14.39:588][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.14.49:589][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.14.59:590][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.15.09:589][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.15.19:589][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.15.29:590][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.15.39:591][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.15.49:592][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.15.59:594][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.16.09:595][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.16.19:596][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.16.29:597][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.16.39:597][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.16.49:598][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.16.59:599][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.17.09:600][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.17.19:601][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.17.28:602][201]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8625.208984
[2025.05.23-18.17.29:603][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.17.29:603][204]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.17.29:603][204]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8625.875000, Update Interval: 319.279755
[2025.05.23-18.17.39:603][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.17.49:604][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.17.59:605][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.18.09:607][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.18.19:608][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.18.29:609][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.18.39:609][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.18.49:609][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.18.59:610][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.19.09:610][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.19.19:611][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.19.29:611][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.19.39:611][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.19.49:615][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.19.59:615][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.20.09:616][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.20.19:616][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.20.29:616][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.20.39:617][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.20.49:618][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.20.59:618][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.21.09:618][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.21.19:618][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.21.29:619][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.21.39:619][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.21.49:620][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.21.59:621][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.22.09:622][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.22.19:622][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.22.29:624][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.22.39:623][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.22.49:624][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.22.59:624][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.23.09:625][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.23.19:628][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.23.21:294][259]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8977.831055
[2025.05.23-18.23.22:295][262]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.23.22:295][262]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8978.497070, Update Interval: 301.014435
[2025.05.23-18.23.29:628][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.23.39:629][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.23.49:630][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.23.59:630][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.24.09:632][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.24.19:632][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.24.29:632][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.24.39:633][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.24.49:634][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.24.59:635][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.25.09:635][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.25.19:638][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.25.29:638][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.25.39:638][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.25.49:639][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.25.59:639][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.26.09:639][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.26.19:640][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.26.29:640][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.26.39:640][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.26.49:642][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.26.59:643][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.27.09:644][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.27.19:644][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.27.29:644][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.27.39:645][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.27.49:646][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.27.59:646][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.28.09:647][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.28.19:648][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.28.29:649][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.28.39:651][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.28.40:317][216]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-39599718&AppEnvironment=********************************&UploadType=eteventstream&SessionID=A66E152841218C09656A88BD4ED89A22, HTTP code: 0, content length: 0, actual payload size: 0
[2025.05.23-18.28.40:318][216]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: request failed, libcurl error: 55 (Failed sending data to the peer)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 0 (Found bundle for host: 0x938d40804e0 [serially])
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 1 (Re-using existing connection with host api.epicgames.dev)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 2 (Send failure: Connection was reset)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 3 (OpenSSL SSL_write: Connection was reset, errno 10054)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 4 (Closing connection)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 5 (Recv failure: Connection was reset)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: 00000938A692DE00: libcurl info message cache 6 (Recv failure: Connection was reset)
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.17.0-39599718&AppEnvironment=********************************&UploadType=eteventstream&SessionID=A66E152841218C09656A88BD4ED89A22
[2025.05.23-18.28.40:318][217]LogEOSSDK: Warning: LogEOS: Failed to connect to the backend. ServiceName=[Metrics], OperationName=[SendBackendEvent], Url=[<Redacted>]
[2025.05.23-18.28.44:317][228]LogHttp: Warning: 00000938D04F7700: request failed, libcurl error: 65 (Send failed since rewinding of the data stream failed)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 0 (Found bundle for host: 0x93774840660 [serially])
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 1 (Re-using existing connection with host datarouter.ol.epicgames.com)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 2 (We are completely uploaded and fine)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 3 (Recv failure: Connection was reset)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 4 (OpenSSL SSL_read: Connection was reset, errno 10054)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 5 (Connection died, retrying a fresh connect (retry count: 1))
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 6 (state.rewindbeforesend = TRUE)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 7 (Closing connection)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 8 (Recv failure: Connection was reset)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 9 (Send failure: Connection was reset)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 10 (Recv failure: Connection was reset)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 11 (Issue another request to this URL: 'https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BB7CFDB98-4966-0470-DA29-C49A03893D7B%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream')
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 12 (  Trying 18.207.79.37:443...)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 13 (Connected to datarouter.ol.epicgames.com (18.207.79.37) port 443)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 14 (ALPN: curl offers http/1.1)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 15 (SSL reusing session ID)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 16 (TLSv1.3 (OUT), TLS handshake, Client hello (1):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 17 (TLSv1.3 (IN), TLS handshake, Server hello (2):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 18 (TLSv1.3 (IN), TLS handshake, Encrypted Extensions (8):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 19 (TLSv1.3 (IN), TLS handshake, Certificate (11):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 20 (TLSv1.3 (IN), TLS handshake, CERT verify (15):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 21 (TLSv1.3 (IN), TLS handshake, Finished (20):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 22 (TLSv1.3 (OUT), TLS change cipher, Change cipher spec (1):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 23 (TLSv1.3 (OUT), TLS handshake, Finished (20):)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 24 (SSL connection using TLSv1.3 / TLS_AES_128_GCM_SHA256)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 25 (ALPN: server accepted http/1.1)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 26 (Server certificate:)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 27 ( subject: CN=datarouter.cfef.live.use1a.on.epicgames.com)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 28 ( start date: Feb  2 00:00:00 2025 GMT)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 29 ( expire date: Mar  3 23:59:59 2026 GMT)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 30 ( subjectAltName: host "datarouter.ol.epicgames.com" matched cert's "datarouter.ol.epicgames.com")
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 31 ( issuer: C=US; O=Amazon; CN=Amazon RSA 2048 M03)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 32 ( SSL certificate verify ok.)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 33 (using HTTP/1.1)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 34 (necessary data rewind wasn't possible)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 35 (Closing connection)
[2025.05.23-18.28.44:318][228]LogHttp: Warning: 00000938D04F7700: libcurl info message cache 36 (TLSv1.3 (OUT), TLS alert, close notify (256):)
[2025.05.23-18.28.49:652][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.28.59:654][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.29.09:653][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.29.19:658][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.29.21:657][340]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9338.074219
[2025.05.23-18.29.22:656][343]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.29.22:656][343]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9338.740234, Update Interval: 310.050964
[2025.05.23-18.29.29:657][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.29.39:658][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.29.49:659][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.29.59:659][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.30.09:663][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.30.19:666][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.30.29:670][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.30.39:672][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.30.49:672][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.30.59:674][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.31.09:675][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.31.19:676][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.31.29:676][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.31.39:676][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.31.49:678][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.31.59:678][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.32.09:678][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.32.19:679][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.32.29:680][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.32.39:682][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.32.49:684][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.32.59:684][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.33.09:686][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.33.19:687][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.33.29:690][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.33.39:689][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.33.49:691][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.33.59:691][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.34.09:691][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.34.19:692][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.34.29:692][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.34.39:693][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.34.49:694][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.34.59:694][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.35.09:694][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.35.16:696][405]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9692.994141
[2025.05.23-18.35.17:696][408]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.35.17:696][408]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9693.660156, Update Interval: 316.606354
[2025.05.23-18.35.19:696][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.35.29:698][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.35.39:699][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.35.49:703][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.35.59:705][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.36.09:706][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.36.19:706][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.36.29:707][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.36.39:708][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.36.49:709][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.36.59:710][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.37.09:712][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.37.19:712][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.37.29:713][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.37.39:714][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.37.49:714][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.37.59:715][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.38.09:718][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.38.19:718][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.38.29:718][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.38.39:720][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.38.49:720][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.38.59:721][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.39.09:723][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.39.19:724][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.39.29:725][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.39.39:726][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.39.49:728][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.39.59:728][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.40.09:729][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.40.19:730][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.40.29:729][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.40.39:730][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.40.49:731][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.40.59:731][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.41.09:731][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.41.14:066][477]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10050.211914
[2025.05.23-18.41.15:067][480]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.41.15:067][480]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10050.877930, Update Interval: 341.236603
[2025.05.23-18.41.19:733][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.41.29:734][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.41.39:734][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.41.49:735][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.41.59:735][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.42.09:736][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.42.19:736][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.42.29:736][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.42.39:736][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.42.49:738][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.42.59:737][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.43.09:738][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.43.19:738][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.43.29:738][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.43.39:738][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.43.49:739][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.43.59:739][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.44.09:739][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.44.19:741][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.44.29:742][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.44.39:742][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.44.49:742][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.44.59:743][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.45.09:745][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.45.19:744][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.45.29:745][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.45.39:747][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.45.49:746][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.45.59:749][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.46.09:749][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.46.19:751][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.46.29:750][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.46.39:751][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.46.49:751][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.46.59:751][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.47.09:752][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.47.19:753][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.47.29:754][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.47.39:756][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.47.49:757][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.47.50:092][665]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10446.177734
[2025.05.23-18.47.51:091][668]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.47.51:091][668]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10446.842773, Update Interval: 301.741394
[2025.05.23-18.47.59:760][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.48.09:759][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.48.19:759][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.48.29:761][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.48.39:763][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.48.49:763][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.48.59:765][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.49.09:766][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.49.19:767][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.49.29:769][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.49.39:769][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.49.49:771][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.49.59:772][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.50.09:772][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.50.19:775][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.50.29:775][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.50.39:775][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.50.49:776][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.50.59:776][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.51.09:776][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.51.19:778][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.51.29:778][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.51.39:779][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.51.49:780][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.51.59:780][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.52.09:782][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.52.19:782][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.52.29:783][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.52.39:784][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.52.49:784][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.52.59:784][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.53.09:786][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.53.19:788][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.53.29:788][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.53.39:790][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.53.43:790][726]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10799.791992
[2025.05.23-18.53.45:458][731]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.53.45:458][731]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10801.125000, Update Interval: 308.155762
[2025.05.23-18.53.49:791][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.53.59:792][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.54.09:793][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.54.19:793][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.54.29:794][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.54.39:795][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.54.49:795][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.54.59:796][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.55.09:797][984]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.55.19:798][ 14]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.55.29:799][ 44]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.55.34:151][ 58]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.23-18.55.39:800][ 74]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.55.49:801][104]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.55.59:801][134]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.56.09:801][164]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.56.19:803][194]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.56.29:803][224]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.56.39:803][254]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.56.49:804][284]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.56.59:804][314]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.57.09:805][344]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.57.19:806][374]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.57.29:807][404]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.57.39:806][434]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.57.49:808][464]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.57.59:809][494]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.58.09:810][524]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.58.19:810][554]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.58.29:810][584]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.58.39:812][614]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.58.49:813][644]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.58.59:815][674]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.59.09:815][704]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.59.19:816][734]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.59.28:817][761]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11144.714844
[2025.05.23-18.59.29:817][764]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.59.29:817][764]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-18.59.29:817][764]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11145.381836, Update Interval: 314.436462
[2025.05.23-18.59.39:818][794]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.59.49:818][824]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-18.59.59:819][854]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.00.09:820][884]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.00.19:823][914]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.00.29:822][944]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.00.39:824][974]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.00.49:825][  4]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.00.59:826][ 34]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.01.09:826][ 64]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.01.19:827][ 94]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.01.29:827][124]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.01.39:829][154]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.01.49:830][184]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.01.59:832][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.02.09:832][244]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.02.19:833][274]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.02.29:833][304]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.02.39:833][334]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.02.49:833][364]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.02.59:835][394]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.03.09:835][424]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.03.19:837][454]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.03.29:837][484]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.03.39:838][514]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.03.49:840][544]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.03.59:840][574]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.04.09:842][604]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.04.19:843][634]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.04.29:843][664]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.04.39:845][694]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.04.49:846][724]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.04.59:846][754]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.05.09:847][784]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.05.16:514][804]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11492.338867
[2025.05.23-19.05.17:514][807]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-19.05.17:514][807]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11493.005859, Update Interval: 314.154480
[2025.05.23-19.05.19:848][814]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.05.29:848][844]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.05.39:848][874]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.05.49:848][904]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.05.59:849][934]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.06.09:850][964]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.06.19:850][994]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.06.29:852][ 24]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.06.39:852][ 54]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.06.49:855][ 84]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.06.59:855][114]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.07.09:856][144]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.07.19:858][174]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.07.29:859][204]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.07.39:860][234]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.07.49:860][264]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.07.59:861][294]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.08.09:861][324]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.08.19:862][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.08.29:863][384]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.08.39:863][414]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.08.49:864][444]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.08.59:864][474]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.09.09:865][504]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.09.19:866][534]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.09.29:866][564]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.09.39:866][594]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.09.49:866][624]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.09.59:868][654]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.10.09:870][684]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.10.19:872][714]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.10.29:873][744]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.10.39:872][774]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.10.49:874][804]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.10.59:875][834]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.11.09:875][864]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.11.19:876][894]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.11.29:878][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.11.30:212][925]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11865.975586
[2025.05.23-19.11.31:211][928]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.05.23-19.11.31:211][928]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11866.641602, Update Interval: 335.862305
[2025.05.23-19.11.39:880][954]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.23-19.11.45:544][971]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Test2/Metahuman.Metahuman
[2025.05.23-19.11.45:544][971]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.23-19.11.45:556][971]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_9:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.05.23-19.11.47:897][ 89]LogSlate: Window 'Metahuman' being destroyed
[2025.05.23-19.11.47:903][ 89]LogSlate: Window 'Metahuman' being destroyed
[2025.05.23-19.11.47:917][ 89]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.23-19.11.47:918][ 89]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.23-19.11.47:985][ 89]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.23-19.11.50:188][240]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
