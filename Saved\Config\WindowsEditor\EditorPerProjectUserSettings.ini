;METADATA=(Diff=true, UseCommands=true)
[/Script/UnrealEd.EditorPerProjectUserSettings]
bAllowSelectTranslucent=False
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=H:/Plugins/BlenderLinkProject/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bConfirmEditorClose=False
bSCSEditorShowFloor=False
bAlwaysBuildUAT=True
SCSViewportCameraSpeed=4
bShowSelectionSubcomponents=True
AssetViewerProfileName=
PreviewFeatureLevel=4
PreviewPlatformName=None
PreviewShaderFormatName=None
PreviewShaderPlatformName=None
bPreviewFeatureLevelActive=False
bPreviewFeatureLevelWasDefault=True
PreviewDeviceProfileName=None

[/Script/UnrealEd.EditorStyleSettings]
ApplicationScale=1.000000
bColorVisionDeficiencyCorrection=False
bColorVisionDeficiencyCorrectionPreviewWithDeficiency=False
SelectionColor=(R=0.828000,G=0.364000,B=0.003000,A=1.000000)
AdditionalSelectionColors[0]=(R=0.019382,G=0.496933,B=1.000000,A=1.000000)
AdditionalSelectionColors[1]=(R=0.356400,G=0.040915,B=0.520996,A=1.000000)
AdditionalSelectionColors[2]=(R=1.000000,G=0.168269,B=0.332452,A=1.000000)
AdditionalSelectionColors[3]=(R=1.000000,G=0.051269,B=0.051269,A=1.000000)
AdditionalSelectionColors[4]=(R=1.000000,G=0.715693,B=0.010330,A=1.000000)
AdditionalSelectionColors[5]=(R=0.258183,G=0.539479,B=0.068478,A=1.000000)
ViewportToolOverlayColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
bEnableEditorWindowBackgroundColor=False
EditorWindowBackgroundColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
MenuSearchFieldVisibilityThreshold=10
bUseGrid=True
RegularColor=(R=0.024000,G=0.024000,B=0.024000,A=1.000000)
RuleColor=(R=0.010000,G=0.010000,B=0.010000,A=1.000000)
CenterColor=(R=0.005000,G=0.005000,B=0.005000,A=1.000000)
GridSnapSize=16
GraphBackgroundBrush=(TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),ResourceObject=None,OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=0.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False),bIsDynamicallyLoaded=False,ResourceName="")
bShowNativeComponentNames=True
AssetEditorOpenLocation=Default
bEnableColorizedEditorTabs=True
CurrentAppliedTheme=134380265FBB4A9CA00A1DC9770217B8

[/Script/UnrealEd.EditorLoadingSavingSettings]
AutoReimportDirectorySettings=(SourceDirectory="/Game/",MountPoint="",Wildcards=((Wildcard="Localization/*")))
AutoReimportDirectorySettings=(SourceDirectory="/Game/",MountPoint="",Wildcards=((Wildcard="*_Ingested/*")))
AutoSaveMaxBackups=10
AutoSaveMethod=BackupAndRestore
bForceCompilationAtStartup=False
RestoreOpenAssetTabsOnRestart=AlwaysPrompt
bMonitorContentDirectories=True
AutoReimportThreshold=3.000000
bAutoCreateAssets=True
bAutoDeleteAssets=True
bDetectChangesOnStartup=True
bPromptBeforeAutoImporting=True
bDeleteSourceFilesWithAssets=False
bAutomaticallyCheckoutOnAssetModification=False
bSCCUseGlobalSettings=False

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
bShouldMinimizeEditorOnNonVRPIE=False
bEmulateStereo=False
SoloAudioInFirstPIEClient=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
bPromoteOutputLogWarningsDuringPIE=False
NewWindowPosition=(X=-1,Y=-1)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNumberOfClients=1
PrimaryPIEClientIndex=0
ServerPort=17777
ClientWindowWidth=640
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
bHMDForPrimaryProcessOnly=True
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=Windows@DESKTOP-E41IK6R
LastExecutedLaunchName=DESKTOP-E41IK6R
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)

[/Script/UnrealEd.LevelEditorViewportSettings]
GridEnabled=False
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
bShowActorEditorContext=True
bAllowEditWidgetAxisDisplay=True
bUseLegacyCameraMovementNotifications=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=True
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
TransformWidgetSizeAdjustment=0
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=0.500000
LastInViewportMenuLocation=(X=0.000000,Y=0.000000)
MaterialForDroppedTextures=None
MaterialParamsForDroppedTextures=()
EditorViews=(("/Game/MetaHumans/Test/TestLevel.TestLevel", (LevelViewportsInfo=((CamPosition=(X=542.473882,Y=1.910855,Z=318.369810)),(CamPosition=(X=542.473882,Y=1.910855,Z=318.369810)),(CamPosition=(X=542.473882,Y=1.910855,Z=318.369810)),(CamPosition=(X=130.263350,Y=87.914039,Z=166.388834),CamRotation=(Pitch=-13.188928,Yaw=1.601827,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=542.473882,Y=1.910855,Z=318.369810),CamUpdated=True)))),("/Temp/Untitled_1.Untitled_1", (LevelViewportsInfo=((CamPosition=(X=542.473882,Y=1.910855,Z=318.369810)),(CamPosition=(X=542.473882,Y=1.910855,Z=318.369810)),(CamPosition=(X=542.473882,Y=1.910855,Z=318.369810)),(CamPosition=(X=203.653413,Y=-147.476405,Z=437.922539),CamRotation=(Pitch=-48.388929,Yaw=-233.398176,Roll=-0.000001)),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True),(CamPosition=(X=542.473882,Y=1.910855,Z=318.369810),CamUpdated=True)))))
PropertyColorationColorForMatchingObjects=(B=0,G=0,R=255,A=255)
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))

[MRU]
MRUItem0=/Game/MetaHumans/Test/TestLevel

[/Script/UnrealEd.PersonaOptions]
bAutoAlignFloorToMesh=True
bAlwaysOpenAnimationAssetsInNewTab=False
bMuteAudio=False
DefaultLocalAxesSelection=2
DefaultBoneDrawSelection=1
bShowBoneColors=False
DefaultBoneColor=(R=0.000000,G=0.000000,B=0.025000,A=1.000000)
SelectedBoneColor=(R=0.200000,G=1.000000,B=0.200000,A=1.000000)
AffectedBoneColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
DisabledBoneColor=(R=0.400000,G=0.400000,B=0.400000,A=1.000000)
ParentOfSelectedBoneColor=(R=0.850000,G=0.450000,B=0.120000,A=1.000000)
VirtualBoneColor=(R=0.400000,G=0.400000,B=1.000000,A=1.000000)
SectionTimingNodeColor=(R=0.390000,G=0.390000,B=1.000000,A=0.750000)
NotifyTimingNodeColor=(R=0.800000,G=0.100000,B=0.100000,A=1.000000)
BranchingPointTimingNodeColor=(R=0.500000,G=1.000000,B=1.000000,A=1.000000)
bPauseAnimationOnCameraMove=False
bUseInlineSocketEditor=False
bFlattenSkeletonHierarchyWhenFiltering=False
bHideParentsWhenFiltering=True
bShowBoneIndexes=False
bExpandTreeOnSelection=True
bAllowPreviewMeshCollectionsToSelectFromDifferentSkeletons=True
bAllowPreviewMeshCollectionsToUseCustomAnimBP=False
bAllowMeshSectionSelection=False
NumFolderFiltersInAssetBrowser=2
AssetEditorOptions=(Context="SkeletonEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="SkeletalMeshEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="AnimationEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="AnimationBlueprintEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="PhysicsAssetEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
AssetEditorOptions=(Context="ControlRigEditor",ViewportConfigs[0]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[1]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[2]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""),ViewportConfigs[3]=(ViewModeIndex=VMI_Lit,ViewFOV=53.430000,CameraSpeedSetting=4,CameraSpeedScalar=1.000000,CameraFollowMode=None,CameraFollowBoneName=""))
CurveEditorSnapInterval=0.010000
TimelineScrubSnapValue=1000
TimelineDisplayFormat=Frames
bTimelineDisplayPercentage=True
bTimelineDisplayFormatSecondary=True
bTimelineDisplayCurveKeys=False
TimelineEnabledSnaps=CompositeSegment
TimelineEnabledSnaps=MontageSection
bAllowIncompatibleSkeletonSelection=False
bUseTreeViewForAnimationCurves=False
AnimationCurveGroupingDelimiters="._/|\\"

[DetailCustomWidgetExpansion]
GeneralProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CryptoKeysSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GameplayTagsSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GameMapsSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MoviePlayerSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ProjectPackagingSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
HardwareTargetingSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AssetManagerSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AssetToolsSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
SlateRHIRendererSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
WidgetStateSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AISystem=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AnimationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AnimationModifierSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AudioSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ChaosSolverSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CineCameraSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CollisionProfile=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ConsoleSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ControlRigSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CookerSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CrowdManager=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
DataDrivenConsoleVariableSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
DebugCameraControllerSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
OptimusSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
EnhancedInputDeveloperSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
InputModifierSmoothDelta=
InputModifierDeadZone=
InputModifierResponseCurveExponential=
InputModifierFOVScaling=
InputTriggerDown=
InputTriggerPressed=
InputTriggerReleased=
InputTriggerHold=
InputTriggerHoldAndRelease=
InputTriggerTap=
InputTriggerPulse=
EnhancedInputEditorProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MegascansMaterialParentSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
InterchangeFbxSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GameplayDebuggerConfig=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GarbageCollectionSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
Engine=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GLTFPipelineSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
HierarchicalLODSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
InputSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
InterchangeProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LandscapeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LevelSequenceProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MassSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MaterialXPipelineSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MeshBudgetProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MeshDrawCommandStatsSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MetaSoundSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
RecastNavMesh=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
NavigationSystemV1=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
NetworkSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ObjectMixerEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
PhysicsSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
RendererSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
RendererOverrideSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
SlateSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
StateTreeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
StreamingSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
TextureEncodingProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
UsdProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
UserInterfaceSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
VirtualTexturePoolConfig=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
WorldPartitionSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LevelEditor2DSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
EditorProjectAppearanceSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
EditorProjectAssetSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
BlueprintEditorProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ClassViewerProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ContentBrowserCollectionProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
DataValidationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
DDCProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
DocumentationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
EditorUtilityWidgetProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ProxyLODMeshSimplificationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LevelEditorProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LevelInstanceEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MovieSceneToolsProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MeshSimplificationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
PaperImporterSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
EditorPerformanceProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
SourceControlPreferences=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
RigVMProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
SkeletalMeshSimplificationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
PlasticSourceControlProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
StructViewerProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
TextureImportSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
UMGEditorProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AndroidRuntimeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ShaderPlatformQualitySettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AndroidSDKSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
IOSRuntimeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LinuxTargetSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MacTargetSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
WindowsTargetSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
XcodeProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AndroidFileServerRuntimeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AppleARKitSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
AvfMediaSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CameraCalibrationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
DataflowSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
FractureModeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GameplayCamerasSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GameplayCamerasEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GeometryCacheStreamerSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GooglePADRuntimeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
GroomPluginSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
HoldoutCompositeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ImgMediaSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ToolPresetProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LevelSequenceEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LiveLinkSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LiveLinkComponentSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
LiveLinkSequencerSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MetaHumanSDKSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ModelingToolsEditorModeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ModelingComponentsSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
CustomizableObjectEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MutableValidationSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
NiagaraSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
NiagaraEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
NNEDenoiserSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
NNERuntimeORTSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
OpenColorIOSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
PaperRuntimeSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
PythonScriptPluginSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
RenderDocPluginSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
ResonanceAudioSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
TakeRecorderProjectSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
MovieSceneTakeSettings=
TakeRecorderMicrophoneAudioSourceSettings=
TakeRecorderMicrophoneAudioManager=
MovieSceneAnimationTrackRecorderEditorSettings=
TakeRecorderWorldSourceSettings=
TcpMessagingSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
TemplateSequenceEditorSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
UdpMessagingSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
WmfMediaSettings=EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Android,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_IOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Linux,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_LinuxArm64,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Mac,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_TVOS,EnhancedInputDeveloperSettings.Enhanced Input.EnhancedInputPlatformSettings_Windows,InputSettings.Platforms.InputPlatformSettings_Android,InputSettings.Platforms.InputPlatformSettings_IOS,InputSettings.Platforms.InputPlatformSettings_Linux,InputSettings.Platforms.InputPlatformSettings_LinuxArm64,InputSettings.Platforms.InputPlatformSettings_Mac,InputSettings.Platforms.InputPlatformSettings_TVOS,InputSettings.Platforms.InputPlatformSettings_Windows
SkeletalMesh=SkeletalMesh.Material Slots.MaterialList,SkeletalMesh.LOD0.SkeletalMeshSectionListNameLOD_0,SkeletalMesh.LOD0.MeshBuildSettings,SkeletalMesh.LOD1.SkeletalMeshSectionListNameLOD_1,SkeletalMesh.LOD1.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD1.MeshBuildSettings,SkeletalMesh.LOD2.SkeletalMeshSectionListNameLOD_2,SkeletalMesh.LOD2.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD2.MeshBuildSettings,SkeletalMesh.LOD3.SkeletalMeshSectionListNameLOD_3,SkeletalMesh.LOD3.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD3.MeshBuildSettings,SkeletalMesh.LOD4.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD4.MeshBuildSettings,SkeletalMesh.LOD5.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD5.MeshBuildSettings,SkeletalMesh.LOD6.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD6.MeshBuildSettings,SkeletalMesh.LOD7.SkeletalMeshOptimizationSettings,SkeletalMesh.LOD7.MeshBuildSettings
LODInfoUILayout=
MeshNaniteSettings=
InterchangeGenericAssetsPipeline=InterchangeGenericAssetsPipeline.Common Meshes.Build,InterchangeGenericAssetsPipeline.Skeletal Meshes.Build,InterchangeGenericAssetsPipeline.Static Meshes.Collision,InterchangeGenericAssetsPipeline.Static Meshes.Build,InterchangeGenericAssetsPipeline.Animations.Curves

[LevelSequenceEditor SequencerSettings]
AutoChangeMode=None
AllowEditsMode=AllEdits
KeyGroupMode=KeyChanged
KeyInterpolation=Auto
SpawnPosition=SSP_Origin
bCreateSpawnableCameras=True
bIsSnapEnabled=True
bSnapKeyTimesToInterval=True
bSnapKeyTimesToKeys=True
bSnapSectionTimesToInterval=True
bSnapSectionTimesToSections=True
bSnapKeysAndSectionsToPlayRange=False
bSnapPlayTimeToKeys=False
bSnapPlayTimeToSections=False
bSnapPlayTimeToMarkers=False
bSnapPlayTimeToInterval=True
bSnapPlayTimeToPressedKey=True
bSnapPlayTimeToDraggedKey=True
bSnapCurveValueToInterval=False
bShowSelectedNodesOnly=False
bRewindOnRecord=False
bLeftMouseDragDoesMarquee=False
ZoomPosition=SZP_CurrentTime
bAutoScrollEnabled=False
bLinkCurveEditorTimeRange=False
bSynchronizeCurveEditorSelection=True
bIsolateCurveEditorToSelection=True
bCurveEditorVisible=True
LoopMode=SLM_NoLoop
bResetPlayheadWhenNavigating=False
bKeepCursorInPlayRangeWhileScrubbing=False
JumpFrameIncrement=(Value=5)
TimeWarpDisplay=Both
bShowLayerBars=True
bShowKeyBars=True
bShowChannelColors=False
bShowInfoButton=True
bShowTickLines=True
bShowSequencerToolbar=True
bShowMarkedFrames=True
KeyAreaCurveExtents=
KeyAreaHeightWithCurves=15.000000
ReduceKeysTolerance=0.000100
bDeleteKeysWhenTrimming=True
bDisableSectionsAfterBaking=True
MarkedFrameColor=(R=0.000000,G=1.000000,B=1.000000,A=0.400000)
SectionColorTints=(B=142,G=102,R=88,A=255)
SectionColorTints=(B=132,G=137,R=99,A=255)
SectionColorTints=(B=92,G=127,R=110,A=255)
SectionColorTints=(B=102,G=142,R=151,A=255)
SectionColorTints=(B=101,G=119,R=147,A=255)
SectionColorTints=(B=108,G=95,R=139,A=255)
SectionColorTints=(B=121,G=74,R=109,A=255)
bCleanPlaybackMode=True
bActivateRealtimeViewports=True
bEvaluateSubSequencesInIsolation=False
bRerunConstructionScripts=True
bShowDebugVisualization=False
bVisualizePreAndPostRoll=True
bCompileDirectorOnEvaluate=True
TrajectoryPathCap=250
MovieRendererName=
bAutoExpandNodesOnSelection=True
bRestoreOriginalViewportOnCameraCutUnlock=True
TreeViewWidth=0.300000
ViewDensity=Relaxed
AssetBrowserWidth=500.000000
AssetBrowserHeight=300.000000
ColumnVisibilitySettings=(ColumnName="Indicator",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Pin",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Lock",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Solo",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Mute",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Label",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Edit",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Add",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="Nav",bIsVisible=True)
ColumnVisibilitySettings=(ColumnName="KeyFrame",bIsVisible=False)
ColumnVisibilitySettings=(ColumnName="ColorPicker",bIsVisible=True)
SidebarState=()
TrackFilterBars=(("LevelSequenceEditor", ()))
bIncludePinnedInFilter=False
bAutoExpandNodesOnFilterPass=False
bUseFilterSubmenusForCategories=False
bFilterBarVisible=False
LastFilterBarLayout=Horizontal
LastFilterBarSizeCoefficient=0.000000
ThumbnailCaptureSettings=(CaptureFrameLocationRule=CurrentFrame)

[EditorStartup]
LastLevel=/Game/MetaHumans/Test/TestLevel

[ModuleFileTracking]
BlenderLinkProject.TimeStamp=2025.05.15-05.03.58
BlenderLinkProject.LastCompileMethod=Unknown
StorageServerClient.TimeStamp=2025.03.24-15.02.14
StorageServerClient.LastCompileMethod=Unknown
CookOnTheFly.TimeStamp=2025.03.24-15.01.58
CookOnTheFly.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.03.24-15.02.14
StreamingFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.03.24-15.02.07
NetworkFile.LastCompileMethod=Unknown
PakFile.TimeStamp=2025.03.24-15.02.07
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.03.24-15.02.11
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.03.24-15.02.11
SandboxFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.03.24-15.01.58
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.03.24-15.02.03
Engine.LastCompileMethod=Unknown
UniversalObjectLocator.TimeStamp=2025.03.24-15.02.15
UniversalObjectLocator.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.03.24-15.02.11
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.03.24-15.01.55
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.03.24-15.02.11
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.03.24-15.02.05
Landscape.LastCompileMethod=Unknown
RHICore.TimeStamp=2025.03.24-15.02.11
RHICore.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.03.24-15.02.11
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.03.24-15.02.14
TextureCompressor.LastCompileMethod=Unknown
OpenColorIOWrapper.TimeStamp=2025.03.24-15.02.07
OpenColorIOWrapper.LastCompileMethod=Unknown
Virtualization.TimeStamp=2025.03.24-15.02.16
Virtualization.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.03.24-15.02.07
MessageLog.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.03.24-15.01.56
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.03.24-15.02.11
PropertyEditor.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.03.24-15.01.55
AnimationModifiers.LastCompileMethod=Unknown
IoStoreOnDemand.TimeStamp=2025.03.24-15.02.05
IoStoreOnDemand.LastCompileMethod=Unknown
OpusAudioDecoder.TimeStamp=2025.03.24-15.02.07
OpusAudioDecoder.LastCompileMethod=Unknown
VorbisAudioDecoder.TimeStamp=2025.03.24-15.02.16
VorbisAudioDecoder.LastCompileMethod=Unknown
AdpcmAudioDecoder.TimeStamp=2025.03.24-15.01.55
AdpcmAudioDecoder.LastCompileMethod=Unknown
BinkAudioDecoder.TimeStamp=2025.03.24-15.01.57
BinkAudioDecoder.LastCompileMethod=Unknown
RadAudioDecoder.TimeStamp=2025.03.24-15.02.11
RadAudioDecoder.LastCompileMethod=Unknown
FastBuildController.TimeStamp=2025.03.24-15.07.42
FastBuildController.LastCompileMethod=Unknown
UbaController.TimeStamp=2025.03.24-15.08.31
UbaController.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.03.24-15.08.41
XGEController.LastCompileMethod=Unknown
PerforceSourceControl.TimeStamp=2025.03.24-15.06.55
PerforceSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.03.24-15.02.11
SourceControl.LastCompileMethod=Unknown
PlasticSourceControl.TimeStamp=2025.03.24-15.06.57
PlasticSourceControl.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.03.24-15.07.32
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.03.24-15.07.32
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoOpenSSL.TimeStamp=2025.03.24-15.07.32
PlatformCryptoOpenSSL.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.03.24-15.07.32
PythonScriptPluginPreload.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.03.24-15.01.59
DesktopPlatform.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.03.24-15.06.55
ChaosCloth.LastCompileMethod=Unknown
ExrReaderGpu.TimeStamp=2025.03.24-15.07.51
ExrReaderGpu.LastCompileMethod=Unknown
MediaIOCore.TimeStamp=2025.03.24-15.07.51
MediaIOCore.LastCompileMethod=Unknown
GPUTextureTransfer.TimeStamp=2025.03.24-15.07.51
GPUTextureTransfer.LastCompileMethod=Unknown
EOSShared.TimeStamp=2025.03.24-15.07.56
EOSShared.LastCompileMethod=Unknown
WmfMedia.TimeStamp=2025.03.24-15.07.54
WmfMedia.LastCompileMethod=Unknown
Media.TimeStamp=2025.03.24-15.02.06
Media.LastCompileMethod=Unknown
OnlineServicesInterface.TimeStamp=2025.03.24-15.07.56
OnlineServicesInterface.LastCompileMethod=Unknown
OnlineServicesCommon.TimeStamp=2025.03.24-15.07.56
OnlineServicesCommon.LastCompileMethod=Unknown
OnlineServicesCommonEngineUtils.TimeStamp=2025.03.24-15.07.56
OnlineServicesCommonEngineUtils.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.03.24-15.08.03
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.03.24-15.02.03
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.03.24-15.02.11
SSL.LastCompileMethod=Unknown
XMPP.TimeStamp=2025.03.24-15.02.18
XMPP.LastCompileMethod=Unknown
WebSockets.TimeStamp=2025.03.24-15.02.16
WebSockets.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.03.24-15.08.05
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.03.24-15.02.11
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.03.24-15.08.05
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.03.24-15.08.05
OnlineBlueprintSupport.LastCompileMethod=Unknown
CameraCalibrationCore.TimeStamp=2025.03.24-15.08.31
CameraCalibrationCore.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.03.24-15.06.41
AISupportModule.LastCompileMethod=Unknown
ACLPlugin.TimeStamp=2025.03.24-15.06.41
ACLPlugin.LastCompileMethod=Unknown
OptimusSettings.TimeStamp=2025.03.24-15.06.52
OptimusSettings.LastCompileMethod=Unknown
OpenColorIO.TimeStamp=2025.03.24-15.06.55
OpenColorIO.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.03.24-15.06.57
PixWinPlugin.LastCompileMethod=Unknown
RenderDocPlugin.TimeStamp=2025.03.24-15.06.57
RenderDocPlugin.LastCompileMethod=Unknown
DatasmithContent.TimeStamp=2025.03.24-15.07.10
DatasmithContent.LastCompileMethod=Unknown
GLTFExporter.TimeStamp=2025.03.24-15.07.10
GLTFExporter.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.03.24-15.07.11
VariantManagerContent.LastCompileMethod=Unknown
EditorPerformance.TimeStamp=2025.03.24-15.07.20
EditorPerformance.LastCompileMethod=Unknown
EditorTelemetry.TimeStamp=2025.03.24-15.07.20
EditorTelemetry.LastCompileMethod=Unknown
NFORDenoise.TimeStamp=2025.03.24-15.07.23
NFORDenoise.LastCompileMethod=Unknown
AnalyticsLog.TimeStamp=2025.03.24-15.07.32
AnalyticsLog.LastCompileMethod=Unknown
AnalyticsHorde.TimeStamp=2025.03.24-15.07.32
AnalyticsHorde.LastCompileMethod=Unknown
StudioTelemetry.TimeStamp=2025.03.24-15.07.32
StudioTelemetry.LastCompileMethod=Unknown
Analytics.TimeStamp=2025.03.24-15.01.55
Analytics.LastCompileMethod=Unknown
TelemetryUtils.TimeStamp=2025.03.24-15.02.14
TelemetryUtils.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.03.24-15.07.42
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.03.24-15.07.42
NiagaraVertexFactories.LastCompileMethod=Unknown
NNEDenoiserShaders.TimeStamp=2025.03.24-15.07.54
NNEDenoiserShaders.LastCompileMethod=Unknown
LauncherChunkInstaller.TimeStamp=2025.03.24-15.08.08
LauncherChunkInstaller.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=2025.03.24-15.08.13
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
ComputeFramework.TimeStamp=2025.03.24-15.08.13
ComputeFramework.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.03.24-15.08.09
ChunkDownloader.LastCompileMethod=Unknown
HairStrandsCore.TimeStamp=2025.03.24-15.08.17
HairStrandsCore.LastCompileMethod=Unknown
WindowsDeviceProfileSelector.TimeStamp=2025.03.24-15.08.30
WindowsDeviceProfileSelector.LastCompileMethod=Unknown
HoldoutComposite.TimeStamp=2025.03.24-15.07.13
HoldoutComposite.LastCompileMethod=Unknown
D3D12RHI.TimeStamp=2025.03.24-15.01.58
D3D12RHI.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.03.24-15.02.18
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.03.24-15.02.03
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.03.24-15.01.56
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.03.24-15.01.58
Chaos.LastCompileMethod=Unknown
GeometryCore.TimeStamp=2025.03.24-15.02.03
GeometryCore.LastCompileMethod=Unknown
ChaosSolverEngine.TimeStamp=2025.03.24-15.01.58
ChaosSolverEngine.LastCompileMethod=Unknown
ChaosVDRuntime.TimeStamp=2025.03.24-15.01.58
ChaosVDRuntime.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.03.24-15.01.59
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.03.24-15.02.11
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.03.24-15.02.05
InputCore.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.03.24-15.02.14
TargetPlatform.LastCompileMethod=Unknown
TurnkeySupport.TimeStamp=2025.03.24-15.02.14
TurnkeySupport.LastCompileMethod=Unknown
TextureFormat.TimeStamp=2025.03.24-15.02.14
TextureFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.03.24-15.02.14
TextureFormatASTC.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.03.24-15.02.14
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.03.24-15.02.14
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.03.24-15.02.14
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.03.24-15.02.14
TextureFormatUncompressed.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.03.24-15.06.57
TextureFormatOodle.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.03.24-15.02.05
ImageWrapper.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.03.24-15.01.37
AndroidTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatformSettings.TimeStamp=2025.03.24-15.01.37
AndroidTargetPlatformSettings.LastCompileMethod=Unknown
AndroidTargetPlatformControls.TimeStamp=2025.03.24-15.01.37
AndroidTargetPlatformControls.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.03.24-15.01.45
IOSTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatformSettings.TimeStamp=2025.03.24-15.01.45
IOSTargetPlatformSettings.LastCompileMethod=Unknown
IOSTargetPlatformControls.TimeStamp=2025.03.24-15.01.45
IOSTargetPlatformControls.LastCompileMethod=Unknown
LinuxTargetPlatform.TimeStamp=2025.03.24-15.01.46
LinuxTargetPlatform.LastCompileMethod=Unknown
LinuxTargetPlatformSettings.TimeStamp=2025.03.24-15.01.46
LinuxTargetPlatformSettings.LastCompileMethod=Unknown
LinuxTargetPlatformControls.TimeStamp=2025.03.24-15.01.46
LinuxTargetPlatformControls.LastCompileMethod=Unknown
LinuxArm64TargetPlatform.TimeStamp=2025.03.24-15.01.46
LinuxArm64TargetPlatform.LastCompileMethod=Unknown
LinuxArm64TargetPlatformSettings.TimeStamp=2025.03.24-15.01.46
LinuxArm64TargetPlatformSettings.LastCompileMethod=Unknown
LinuxArm64TargetPlatformControls.TimeStamp=2025.03.24-15.01.46
LinuxArm64TargetPlatformControls.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.03.24-15.02.06
MacTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatformSettings.TimeStamp=2025.03.24-15.02.06
MacTargetPlatformSettings.LastCompileMethod=Unknown
MacTargetPlatformControls.TimeStamp=2025.03.24-15.02.06
MacTargetPlatformControls.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.03.24-15.01.45
TVOSTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatformSettings.TimeStamp=2025.03.24-15.01.45
TVOSTargetPlatformSettings.LastCompileMethod=Unknown
TVOSTargetPlatformControls.TimeStamp=2025.03.24-15.01.45
TVOSTargetPlatformControls.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.03.24-15.02.18
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatformSettings.TimeStamp=2025.03.24-15.02.18
WindowsTargetPlatformSettings.LastCompileMethod=Unknown
WindowsTargetPlatformControls.TimeStamp=2025.03.24-15.02.18
WindowsTargetPlatformControls.LastCompileMethod=Unknown
AudioFormatOPUS.TimeStamp=2025.03.24-15.01.56
AudioFormatOPUS.LastCompileMethod=Unknown
AudioFormatOGG.TimeStamp=2025.03.24-15.01.56
AudioFormatOGG.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.03.24-15.01.56
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatBINK.TimeStamp=2025.03.24-15.01.56
AudioFormatBINK.LastCompileMethod=Unknown
AudioFormatRAD.TimeStamp=2025.03.24-15.01.56
AudioFormatRAD.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.03.24-15.02.11
ShaderFormatVectorVM.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.03.24-15.02.11
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.03.24-15.02.11
ShaderFormatOpenGL.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.03.24-15.02.16
VulkanShaderFormat.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.03.24-15.02.07
MetalShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.03.24-15.01.59
DerivedDataCache.LastCompileMethod=Unknown
ShaderPreprocessor.TimeStamp=2025.03.24-15.02.11
ShaderPreprocessor.LastCompileMethod=Unknown
NullInstallBundleManager.TimeStamp=2025.03.24-15.02.07
NullInstallBundleManager.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.03.24-15.01.56
AssetRegistry.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.03.24-15.02.07
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.03.24-15.02.06
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.03.24-15.02.07
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.03.24-15.02.07
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.03.24-15.02.11
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.03.24-15.06.59
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.03.24-15.07.32
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.03.24-15.02.07
MeshBoneReduction.LastCompileMethod=Unknown
StaticMeshDescription.TimeStamp=2025.03.24-15.02.11
StaticMeshDescription.LastCompileMethod=Unknown
GeometryProcessingInterfaces.TimeStamp=2025.03.24-15.02.03
GeometryProcessingInterfaces.LastCompileMethod=Unknown
NaniteBuilder.TimeStamp=2025.03.24-15.02.07
NaniteBuilder.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.03.24-15.02.07
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.03.24-15.02.05
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.03.24-15.02.07
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.03.24-15.02.11
Sequencer.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.03.24-15.01.58
CurveEditor.LastCompileMethod=Unknown
AssetDefinition.TimeStamp=2025.03.24-15.01.56
AssetDefinition.LastCompileMethod=Unknown
Core.TimeStamp=2025.03.24-15.01.58
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.03.24-15.02.07
Networking.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.03.24-15.02.05
LiveCoding.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.03.24-15.02.03
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.03.24-15.02.11
SourceCodeAccess.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.03.24-15.02.07
Messaging.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.03.24-15.02.07
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.03.24-15.02.16
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.03.24-15.02.05
LandscapeEditorUtilities.LastCompileMethod=Unknown
SubobjectDataInterface.TimeStamp=2025.03.24-15.02.14
SubobjectDataInterface.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.03.24-15.02.11
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.03.24-15.02.11
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.03.24-15.02.11
SlateReflector.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.03.24-15.01.59
EditorStyle.LastCompileMethod=Unknown
UMG.TimeStamp=2025.03.24-15.02.14
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.03.24-15.02.15
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.03.24-15.01.56
AssetTools.LastCompileMethod=Unknown
ScriptableEditorWidgets.TimeStamp=2025.03.24-15.02.11
ScriptableEditorWidgets.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.03.24-15.01.58
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.03.24-15.02.18
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.03.24-15.02.03
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.03.24-15.01.56
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.03.24-15.02.03
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.03.24-15.02.14
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.03.24-15.02.16
VREditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.03.24-15.02.07
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.03.24-15.02.07
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.03.24-15.02.06
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.03.24-15.01.58
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.03.24-15.01.58
ClothingSystemEditor.LastCompileMethod=Unknown
AnimationDataController.TimeStamp=2025.03.24-15.01.55
AnimationDataController.LastCompileMethod=Unknown
TimeManagement.TimeStamp=2025.03.24-15.02.14
TimeManagement.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.03.24-15.01.55
AnimGraph.LastCompileMethod=Unknown
WorldPartitionEditor.TimeStamp=2025.03.24-15.02.18
WorldPartitionEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.03.24-15.02.07
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.03.24-15.02.07
NetworkReplayStreaming.LastCompileMethod=Unknown
MassEntity.TimeStamp=2025.03.24-15.02.06
MassEntity.LastCompileMethod=Unknown
MassEntityTestSuite.TimeStamp=2025.03.24-15.02.06
MassEntityTestSuite.LastCompileMethod=Unknown
AndroidFileServer.TimeStamp=2025.03.24-15.08.08
AndroidFileServer.LastCompileMethod=Unknown
WebMMoviePlayer.TimeStamp=2025.03.24-15.08.30
WebMMoviePlayer.LastCompileMethod=Unknown
WindowsMoviePlayer.TimeStamp=2025.03.24-15.08.30
WindowsMoviePlayer.LastCompileMethod=Unknown
EnhancedInput.TimeStamp=2025.03.24-15.06.59
EnhancedInput.LastCompileMethod=Unknown
InputBlueprintNodes.TimeStamp=2025.03.24-15.06.59
InputBlueprintNodes.LastCompileMethod=Unknown
BlueprintGraph.TimeStamp=2025.03.24-15.01.57
BlueprintGraph.LastCompileMethod=Unknown
MutableRuntime.TimeStamp=2025.03.24-15.07.54
MutableRuntime.LastCompileMethod=Unknown
CustomizableObject.TimeStamp=2025.03.24-15.07.54
CustomizableObject.LastCompileMethod=Unknown
MutableTools.TimeStamp=2025.03.24-15.07.54
MutableTools.LastCompileMethod=Unknown
MutableValidation.TimeStamp=2025.03.24-15.07.54
MutableValidation.LastCompileMethod=Unknown
ImgMediaEngine.TimeStamp=2025.03.24-15.07.51
ImgMediaEngine.LastCompileMethod=Unknown
Paper2D.TimeStamp=2025.03.24-15.06.41
Paper2D.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.03.24-15.06.41
EnvironmentQueryEditor.LastCompileMethod=Unknown
AnimationData.TimeStamp=2025.03.24-15.06.41
AnimationData.LastCompileMethod=Unknown
ControlRig.TimeStamp=2025.03.24-15.06.42
ControlRig.LastCompileMethod=Unknown
Constraints.TimeStamp=2025.03.24-15.01.58
Constraints.LastCompileMethod=Unknown
ControlRigDeveloper.TimeStamp=2025.03.24-15.06.42
ControlRigDeveloper.LastCompileMethod=Unknown
OptimusCore.TimeStamp=2025.03.24-15.06.52
OptimusCore.LastCompileMethod=Unknown
OptimusDeveloper.TimeStamp=2025.03.24-15.06.52
OptimusDeveloper.LastCompileMethod=Unknown
IKRig.TimeStamp=2025.03.24-15.06.52
IKRig.LastCompileMethod=Unknown
IKRigDeveloper.TimeStamp=2025.03.24-15.06.52
IKRigDeveloper.LastCompileMethod=Unknown
RigLogicLib.TimeStamp=2025.03.24-15.06.52
RigLogicLib.LastCompileMethod=Unknown
RigLogicLibTest.TimeStamp=2025.03.24-15.06.52
RigLogicLibTest.LastCompileMethod=Unknown
RigLogicDeveloper.TimeStamp=2025.03.24-15.06.52
RigLogicDeveloper.LastCompileMethod=Unknown
EngineCameras.TimeStamp=2025.03.24-15.06.52
EngineCameras.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.03.24-15.06.52
GameplayCameras.LastCompileMethod=Unknown
OpenColorIOEditor.TimeStamp=2025.03.24-15.06.55
OpenColorIOEditor.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.03.24-15.02.14
ToolMenus.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.03.24-15.06.55
AnimationSharing.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.03.24-15.06.57
PropertyAccessNode.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.03.24-15.06.57
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.03.24-15.02.14
TreeMap.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.03.24-15.01.58
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.03.24-15.01.58
ContentBrowserData.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.03.24-15.02.05
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.03.24-15.02.06
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.03.24-15.02.03
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.03.24-15.01.58
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.03.24-15.02.08
PixelInspectorModule.LastCompileMethod=Unknown
DataValidation.TimeStamp=2025.03.24-15.06.57
DataValidation.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.03.24-15.06.57
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.03.24-15.06.57
FacialAnimationEditor.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.03.24-15.06.57
GameplayTagsEditor.LastCompileMethod=Unknown
ChaosCaching.TimeStamp=2025.03.24-15.07.13
ChaosCaching.LastCompileMethod=Unknown
ChaosCachingEditor.TimeStamp=2025.03.24-15.07.13
ChaosCachingEditor.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.03.24-15.08.40
TakeRecorder.LastCompileMethod=Unknown
FullBodyIK.TimeStamp=2025.03.24-15.07.20
FullBodyIK.LastCompileMethod=Unknown
PBIK.TimeStamp=2025.03.24-15.07.20
PBIK.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.03.24-15.07.32
PythonScriptPlugin.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.03.24-15.07.42
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.03.24-15.07.42
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.03.24-15.07.42
NiagaraEditor.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.03.24-15.02.05
LevelSequence.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.03.24-15.02.11
SignalProcessing.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.03.24-15.07.42
NiagaraAnimNotifies.LastCompileMethod=Unknown
NiagaraSimCaching.TimeStamp=2025.03.24-15.07.46
NiagaraSimCaching.LastCompileMethod=Unknown
NiagaraSimCachingEditor.TimeStamp=2025.03.24-15.07.46
NiagaraSimCachingEditor.LastCompileMethod=Unknown
InterchangeNodes.TimeStamp=2025.03.24-15.07.47
InterchangeNodes.LastCompileMethod=Unknown
InterchangeFactoryNodes.TimeStamp=2025.03.24-15.07.47
InterchangeFactoryNodes.LastCompileMethod=Unknown
InterchangeImport.TimeStamp=2025.03.24-15.07.47
InterchangeImport.LastCompileMethod=Unknown
InterchangePipelines.TimeStamp=2025.03.24-15.07.47
InterchangePipelines.LastCompileMethod=Unknown
MetaHumanMeshTracker.TimeStamp=2025.02.12-07.26.43
MetaHumanMeshTracker.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.03.24-15.07.54
TcpMessaging.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.03.24-15.07.54
UdpMessaging.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.03.24-15.07.54
ActorSequence.LastCompileMethod=Unknown
NNERuntimeORT.TimeStamp=2025.03.24-15.07.56
NNERuntimeORT.LastCompileMethod=Unknown
NNEEditor.TimeStamp=2025.03.24-15.02.07
NNEEditor.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.03.24-15.08.09
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.03.24-15.08.09
AudioSynesthesia.LastCompileMethod=Unknown
AudioAnalyzer.TimeStamp=2025.03.24-15.01.56
AudioAnalyzer.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.03.24-15.08.09
CableComponent.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.03.24-15.08.13
CustomMeshComponent.LastCompileMethod=Unknown
LocationServicesBPLibrary.TimeStamp=2025.03.24-15.08.19
LocationServicesBPLibrary.LastCompileMethod=Unknown
MetasoundGraphCore.TimeStamp=2025.03.24-15.08.20
MetasoundGraphCore.LastCompileMethod=Unknown
MetasoundGenerator.TimeStamp=2025.03.24-15.08.20
MetasoundGenerator.LastCompileMethod=Unknown
MetasoundFrontend.TimeStamp=2025.03.24-15.08.20
MetasoundFrontend.LastCompileMethod=Unknown
MetasoundStandardNodes.TimeStamp=2025.03.24-15.08.22
MetasoundStandardNodes.LastCompileMethod=Unknown
MetasoundEngine.TimeStamp=2025.03.24-15.08.20
MetasoundEngine.LastCompileMethod=Unknown
WaveTable.TimeStamp=2025.03.24-15.08.30
WaveTable.LastCompileMethod=Unknown
MetasoundEngineTest.TimeStamp=2025.03.24-15.08.20
MetasoundEngineTest.LastCompileMethod=Unknown
MetasoundEditor.TimeStamp=2025.03.24-15.08.20
MetasoundEditor.LastCompileMethod=Unknown
AudioWidgets.TimeStamp=2025.03.24-15.08.09
AudioWidgets.LastCompileMethod=Unknown
AdvancedWidgets.TimeStamp=2025.03.24-15.01.55
AdvancedWidgets.LastCompileMethod=Unknown
MsQuicRuntime.TimeStamp=2025.03.24-15.08.23
MsQuicRuntime.LastCompileMethod=Unknown
ResonanceAudio.TimeStamp=2025.03.24-15.08.29
ResonanceAudio.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.03.24-15.08.28
PropertyAccessEditor.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.03.24-15.08.28
ProceduralMeshComponent.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.03.24-15.08.30
SignificanceManager.LastCompileMethod=Unknown
RigVM.TimeStamp=2025.03.24-15.08.29
RigVM.LastCompileMethod=Unknown
RigVMDeveloper.TimeStamp=2025.03.24-15.08.29
RigVMDeveloper.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.03.24-15.08.30
SoundFields.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.03.24-15.08.30
Synthesis.LastCompileMethod=Unknown
StateTreeModule.TimeStamp=2025.03.24-15.08.30
StateTreeModule.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.03.24-15.02.14
TraceServices.LastCompileMethod=Unknown
TraceAnalysis.TimeStamp=2025.03.24-15.02.14
TraceAnalysis.LastCompileMethod=Unknown
StateTreeTestSuite.TimeStamp=2025.03.24-15.08.30
StateTreeTestSuite.LastCompileMethod=Unknown
SQLiteCore.TimeStamp=2025.03.24-15.08.13
SQLiteCore.LastCompileMethod=Unknown
Concert.TimeStamp=2025.03.24-15.06.55
Concert.LastCompileMethod=Unknown
ConcertClient.TimeStamp=2025.03.24-15.06.55
ConcertClient.LastCompileMethod=Unknown
ConcertTransport.TimeStamp=2025.03.24-15.06.55
ConcertTransport.LastCompileMethod=Unknown
ConcertServer.TimeStamp=2025.03.24-15.06.55
ConcertServer.LastCompileMethod=Unknown
RD.TimeStamp=2025.05.12-16.49.43
RD.LastCompileMethod=Unknown
RiderLink.TimeStamp=2025.05.12-16.49.57
RiderLink.LastCompileMethod=Unknown
RiderLogging.TimeStamp=2025.05.12-16.50.11
RiderLogging.LastCompileMethod=Unknown
RiderDebuggerSupport.TimeStamp=2025.05.12-16.49.59
RiderDebuggerSupport.LastCompileMethod=Unknown
ConcertSyncCore.TimeStamp=2025.03.24-15.06.55
ConcertSyncCore.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.03.24-15.06.55
ChaosClothEditor.LastCompileMethod=Unknown
ChaosVD.TimeStamp=2025.03.24-15.06.55
ChaosVD.LastCompileMethod=Unknown
ChaosVDBlueprint.TimeStamp=2025.03.24-15.06.55
ChaosVDBlueprint.LastCompileMethod=Unknown
InputEditor.TimeStamp=2025.03.24-15.06.59
InputEditor.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.03.24-15.07.54
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.03.24-15.07.54
MeshPaintingToolset.LastCompileMethod=Unknown
CustomizableObjectEditor.TimeStamp=2025.03.24-15.07.54
CustomizableObjectEditor.LastCompileMethod=Unknown
RenderGraphInsights.TimeStamp=2025.03.24-15.08.08
RenderGraphInsights.LastCompileMethod=Unknown
TraceUtilities.TimeStamp=2025.03.24-15.08.31
TraceUtilities.LastCompileMethod=Unknown
EditorTraceUtilities.TimeStamp=2025.03.24-15.08.31
EditorTraceUtilities.LastCompileMethod=Unknown
TraceTools.TimeStamp=2025.03.24-15.02.14
TraceTools.LastCompileMethod=Unknown
WorldMetricsCore.TimeStamp=2025.03.24-15.08.41
WorldMetricsCore.LastCompileMethod=Unknown
WorldMetricsTest.TimeStamp=2025.03.24-15.08.41
WorldMetricsTest.LastCompileMethod=Unknown
CsvMetrics.TimeStamp=2025.03.24-15.08.41
CsvMetrics.LastCompileMethod=Unknown
AlembicImporter.TimeStamp=2025.03.24-15.07.46
AlembicImporter.LastCompileMethod=Unknown
AlembicLibrary.TimeStamp=2025.03.24-15.07.46
AlembicLibrary.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.03.24-15.08.14
GeometryCache.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.03.24-15.08.14
GeometryCacheEd.LastCompileMethod=Unknown
ImgMedia.TimeStamp=2025.03.24-15.07.51
ImgMedia.LastCompileMethod=Unknown
MediaCompositing.TimeStamp=2025.03.24-15.07.51
MediaCompositing.LastCompileMethod=Unknown
MediaPlate.TimeStamp=2025.03.24-15.07.51
MediaPlate.LastCompileMethod=Unknown
MediaPlateEditor.TimeStamp=2025.03.24-15.07.51
MediaPlateEditor.LastCompileMethod=Unknown
OnlineBase.TimeStamp=2025.03.24-15.07.56
OnlineBase.LastCompileMethod=Unknown
InterchangeTests.TimeStamp=2025.03.24-15.08.31
InterchangeTests.LastCompileMethod=Unknown
InterchangeTestEditor.TimeStamp=2025.03.24-15.08.31
InterchangeTestEditor.LastCompileMethod=Unknown
CameraCalibrationCoreEditor.TimeStamp=2025.03.24-15.08.31
CameraCalibrationCoreEditor.LastCompileMethod=Unknown
CaptureDataCore.TimeStamp=2025.03.24-15.08.31
CaptureDataCore.LastCompileMethod=Unknown
CaptureDataEditor.TimeStamp=2025.03.24-15.08.31
CaptureDataEditor.LastCompileMethod=Unknown
CaptureDataUtils.TimeStamp=2025.05.22-18.27.46
CaptureDataUtils.LastCompileMethod=External
LensComponent.TimeStamp=2025.03.24-15.08.39
LensComponent.LastCompileMethod=Unknown
LensComponentEditor.TimeStamp=2025.03.24-15.08.39
LensComponentEditor.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.03.24-15.08.40
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.03.24-15.08.40
TakeSequencer.LastCompileMethod=Unknown
Paper2DEditor.TimeStamp=2025.03.24-15.06.41
Paper2DEditor.LastCompileMethod=Unknown
PaperSpriteSheetImporter.TimeStamp=2025.03.24-15.06.41
PaperSpriteSheetImporter.LastCompileMethod=Unknown
PaperTiledImporter.TimeStamp=2025.03.24-15.06.41
PaperTiledImporter.LastCompileMethod=Unknown
ACLPluginEditor.TimeStamp=2025.03.24-15.06.41
ACLPluginEditor.LastCompileMethod=Unknown
AnimationModifierLibrary.TimeStamp=2025.03.24-15.06.42
AnimationModifierLibrary.LastCompileMethod=Unknown
BlendSpaceMotionAnalysis.TimeStamp=2025.03.24-15.06.42
BlendSpaceMotionAnalysis.LastCompileMethod=Unknown
ControlRigSpline.TimeStamp=2025.03.24-15.06.52
ControlRigSpline.LastCompileMethod=Unknown
LiveLink.TimeStamp=2025.03.24-15.06.52
LiveLink.LastCompileMethod=Unknown
LiveLinkComponents.TimeStamp=2025.03.24-15.06.52
LiveLinkComponents.LastCompileMethod=Unknown
LiveLinkEditor.TimeStamp=2025.03.24-15.06.52
LiveLinkEditor.LastCompileMethod=Unknown
LiveLinkGraphNode.TimeStamp=2025.03.24-15.06.52
LiveLinkGraphNode.LastCompileMethod=Unknown
LiveLinkMovieScene.TimeStamp=2025.03.24-15.06.52
LiveLinkMovieScene.LastCompileMethod=Unknown
LiveLinkSequencer.TimeStamp=2025.03.24-15.06.52
LiveLinkSequencer.LastCompileMethod=Unknown
RigLogicModule.TimeStamp=2025.03.24-15.06.52
RigLogicModule.LastCompileMethod=Unknown
RigLogicEditor.TimeStamp=2025.03.24-15.06.52
RigLogicEditor.LastCompileMethod=Unknown
GameplayCamerasUncookedOnly.TimeStamp=2025.03.24-15.06.55
GameplayCamerasUncookedOnly.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.03.24-15.06.55
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
CLionSourceCodeAccess.TimeStamp=2025.03.24-15.06.55
CLionSourceCodeAccess.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.03.24-15.06.55
AnimationSharingEd.LastCompileMethod=Unknown
DumpGPUServices.TimeStamp=2025.03.24-15.06.55
DumpGPUServices.LastCompileMethod=Unknown
GitSourceControl.TimeStamp=2025.03.24-15.06.55
GitSourceControl.LastCompileMethod=Unknown
N10XSourceCodeAccess.TimeStamp=2025.03.24-15.06.55
N10XSourceCodeAccess.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.03.24-15.06.57
PluginUtils.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.03.24-15.06.57
RiderSourceCodeAccess.LastCompileMethod=Unknown
SubversionSourceControl.TimeStamp=2025.03.24-15.06.57
SubversionSourceControl.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.03.24-15.06.57
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.03.24-15.06.57
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.03.24-15.06.57
UObjectPlugin.LastCompileMethod=Unknown
BlueprintHeaderView.TimeStamp=2025.03.24-15.06.57
BlueprintHeaderView.LastCompileMethod=Unknown
ChangelistReview.TimeStamp=2025.03.24-15.06.57
ChangelistReview.LastCompileMethod=Unknown
ColorGradingEditor.TimeStamp=2025.03.24-15.06.57
ColorGradingEditor.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.03.24-15.06.57
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.03.24-15.06.57
CryptoKeysOpenSSL.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.03.24-15.06.57
CurveEditorTools.LastCompileMethod=Unknown
EditorDebugTools.TimeStamp=2025.03.24-15.06.57
EditorDebugTools.LastCompileMethod=Unknown
EditorScriptingUtilities.TimeStamp=2025.03.24-15.06.57
EditorScriptingUtilities.LastCompileMethod=Unknown
MeshLODToolset.TimeStamp=2025.03.24-15.06.57
MeshLODToolset.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.03.24-15.06.57
MaterialAnalyzer.LastCompileMethod=Unknown
MobileLauncherProfileWizard.TimeStamp=2025.03.24-15.06.57
MobileLauncherProfileWizard.LastCompileMethod=Unknown
ModelingToolsEditorMode.TimeStamp=2025.03.24-15.06.57
ModelingToolsEditorMode.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.03.24-15.06.57
PluginBrowser.LastCompileMethod=Unknown
SequencerAnimTools.TimeStamp=2025.03.24-15.06.59
SequencerAnimTools.LastCompileMethod=Unknown
SpeedTreeImporter.TimeStamp=2025.03.24-15.06.59
SpeedTreeImporter.LastCompileMethod=Unknown
StylusInput.TimeStamp=2025.03.24-15.06.59
StylusInput.LastCompileMethod=Unknown
StylusInputDebugWidget.TimeStamp=2025.03.24-15.06.59
StylusInputDebugWidget.LastCompileMethod=Unknown
WorldPartitionHLODUtilities.TimeStamp=2025.03.24-15.06.59
WorldPartitionHLODUtilities.LastCompileMethod=Unknown
UMGWidgetPreview.TimeStamp=2025.03.24-15.06.59
UMGWidgetPreview.LastCompileMethod=Unknown
UVEditor.TimeStamp=2025.03.24-15.06.59
UVEditor.LastCompileMethod=Unknown
UVEditorTools.TimeStamp=2025.03.24-15.06.59
UVEditorTools.LastCompileMethod=Unknown
UVEditorToolsEditorOnly.TimeStamp=2025.03.24-15.06.59
UVEditorToolsEditorOnly.LastCompileMethod=Unknown
DatasmithContentEditor.TimeStamp=2025.03.24-15.07.10
DatasmithContentEditor.LastCompileMethod=Unknown
VariantManager.TimeStamp=2025.03.24-15.07.11
VariantManager.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.03.24-15.07.11
VariantManagerContentEditor.LastCompileMethod=Unknown
AdvancedRenamer.TimeStamp=2025.03.24-15.07.11
AdvancedRenamer.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.03.24-15.07.13
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.03.24-15.07.13
AutomationUtilsEditor.LastCompileMethod=Unknown
BackChannel.TimeStamp=2025.03.24-15.07.13
BackChannel.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.03.24-15.07.13
FractureEditor.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.03.24-15.07.13
ChaosSolverEditor.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.03.24-15.07.13
ChaosNiagara.LastCompileMethod=Unknown
ChaosUserDataPT.TimeStamp=2025.03.24-15.07.13
ChaosUserDataPT.LastCompileMethod=Unknown
DataflowAssetTools.TimeStamp=2025.03.24-15.07.20
DataflowAssetTools.LastCompileMethod=Unknown
DataflowEnginePlugin.TimeStamp=2025.03.24-15.07.20
DataflowEnginePlugin.LastCompileMethod=Unknown
DataflowSimulation.TimeStamp=2025.03.24-15.01.58
DataflowSimulation.LastCompileMethod=Unknown
DataflowNodes.TimeStamp=2025.03.24-15.07.20
DataflowNodes.LastCompileMethod=Unknown
TedsCore.TimeStamp=2025.03.24-15.07.20
TedsCore.LastCompileMethod=Unknown
TypedElementFramework.TimeStamp=2025.03.24-15.02.14
TypedElementFramework.LastCompileMethod=Unknown
MassEntityEditor.TimeStamp=2025.03.24-15.02.06
MassEntityEditor.LastCompileMethod=Unknown
MassEntityDebugger.TimeStamp=2025.03.24-15.02.06
MassEntityDebugger.LastCompileMethod=Unknown
TedsUI.TimeStamp=2025.03.24-15.07.20
TedsUI.LastCompileMethod=Unknown
GeometryFlowCore.TimeStamp=2025.03.24-15.07.20
GeometryFlowCore.LastCompileMethod=Unknown
GeometryFlowMeshProcessing.TimeStamp=2025.03.24-15.07.20
GeometryFlowMeshProcessing.LastCompileMethod=Unknown
GeometryFlowMeshProcessingEditor.TimeStamp=2025.03.24-15.07.20
GeometryFlowMeshProcessingEditor.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.03.24-15.07.20
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.03.24-15.07.20
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.03.24-15.07.20
GeometryCollectionSequencer.LastCompileMethod=Unknown
GeometryCollectionEngine.TimeStamp=2025.03.24-15.02.03
GeometryCollectionEngine.LastCompileMethod=Unknown
GeometryCollectionNodes.TimeStamp=2025.03.24-15.07.20
GeometryCollectionNodes.LastCompileMethod=Unknown
GeometryCollectionDepNodes.TimeStamp=2025.03.24-15.07.20
GeometryCollectionDepNodes.LastCompileMethod=Unknown
LocalizableMessage.TimeStamp=2025.03.24-15.07.21
LocalizableMessage.LastCompileMethod=Unknown
LocalizableMessageBlueprint.TimeStamp=2025.03.24-15.07.21
LocalizableMessageBlueprint.LastCompileMethod=Unknown
MeshModelingToolsExp.TimeStamp=2025.03.24-15.07.21
MeshModelingToolsExp.LastCompileMethod=Unknown
MeshModelingToolsEditorOnlyExp.TimeStamp=2025.03.24-15.07.21
MeshModelingToolsEditorOnlyExp.LastCompileMethod=Unknown
GeometryProcessingAdapters.TimeStamp=2025.03.24-15.07.21
GeometryProcessingAdapters.LastCompileMethod=Unknown
ModelingEditorUI.TimeStamp=2025.03.24-15.07.21
ModelingEditorUI.LastCompileMethod=Unknown
ModelingUI.TimeStamp=2025.03.24-15.07.21
ModelingUI.LastCompileMethod=Unknown
SkeletalMeshModifiers.TimeStamp=2025.03.24-15.07.21
SkeletalMeshModifiers.LastCompileMethod=Unknown
RigLogicMutable.TimeStamp=2025.03.24-15.07.32
RigLogicMutable.LastCompileMethod=Unknown
RigLogicMutableEditor.TimeStamp=2025.03.24-15.07.32
RigLogicMutableEditor.LastCompileMethod=Unknown
ToolPresetAsset.TimeStamp=2025.03.24-15.07.42
ToolPresetAsset.LastCompileMethod=Unknown
ToolPresetEditor.TimeStamp=2025.03.24-15.07.42
ToolPresetEditor.LastCompileMethod=Unknown
NiagaraBlueprintNodes.TimeStamp=2025.03.24-15.07.42
NiagaraBlueprintNodes.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.03.24-15.07.42
NiagaraEditorWidgets.LastCompileMethod=Unknown
InterchangeEditor.TimeStamp=2025.03.24-15.07.46
InterchangeEditor.LastCompileMethod=Unknown
InterchangeEditorPipelines.TimeStamp=2025.03.24-15.07.47
InterchangeEditorPipelines.LastCompileMethod=Unknown
InterchangeEditorUtilities.TimeStamp=2025.03.24-15.07.47
InterchangeEditorUtilities.LastCompileMethod=Unknown
GLTFCore.TimeStamp=2025.03.24-15.07.47
GLTFCore.LastCompileMethod=Unknown
InterchangeMessages.TimeStamp=2025.03.24-15.07.47
InterchangeMessages.LastCompileMethod=Unknown
InterchangeExport.TimeStamp=2025.03.24-15.07.47
InterchangeExport.LastCompileMethod=Unknown
InterchangeDispatcher.TimeStamp=2025.03.24-15.07.47
InterchangeDispatcher.LastCompileMethod=Unknown
InterchangeCommon.TimeStamp=2025.03.24-15.07.47
InterchangeCommon.LastCompileMethod=Unknown
InterchangeCommonParser.TimeStamp=2025.03.24-15.07.47
InterchangeCommonParser.LastCompileMethod=Unknown
InterchangeFbxParser.TimeStamp=2025.03.24-15.07.47
InterchangeFbxParser.LastCompileMethod=Unknown
MetaHumanCore.TimeStamp=2025.02.12-07.26.43
MetaHumanCore.LastCompileMethod=Unknown
MetaHumanCoreEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanCoreEditor.LastCompileMethod=Unknown
MetaHumanImageViewer.TimeStamp=2025.02.12-07.26.43
MetaHumanImageViewer.LastCompileMethod=Unknown
MetaHumanPipeline.TimeStamp=2025.02.12-07.26.44
MetaHumanPipeline.LastCompileMethod=Unknown
MetaHumanFaceContourTracker.TimeStamp=2025.02.12-07.26.43
MetaHumanFaceContourTracker.LastCompileMethod=Unknown
MetaHumanFaceContourTrackerEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanFaceContourTrackerEditor.LastCompileMethod=Unknown
MetaHumanIdentity.TimeStamp=2025.02.12-07.26.43
MetaHumanIdentity.LastCompileMethod=Unknown
MetaHumanIdentityEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanIdentityEditor.LastCompileMethod=Unknown
MetaHumanCaptureData.TimeStamp=2025.02.12-07.26.43
MetaHumanCaptureData.LastCompileMethod=Unknown
MetaHumanCaptureDataEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanCaptureDataEditor.LastCompileMethod=Unknown
MetaHumanCaptureSource.TimeStamp=2025.02.12-07.26.43
MetaHumanCaptureSource.LastCompileMethod=Unknown
MetaHumanFootageIngest.TimeStamp=2025.02.12-07.26.43
MetaHumanFootageIngest.LastCompileMethod=Unknown
MetaHumanCaptureUtils.TimeStamp=2025.02.12-07.26.43
MetaHumanCaptureUtils.LastCompileMethod=Unknown
MetaHumanCaptureProtocolStack.TimeStamp=2025.02.12-07.26.43
MetaHumanCaptureProtocolStack.LastCompileMethod=Unknown
MetaHumanToolkit.TimeStamp=2025.02.12-07.26.44
MetaHumanToolkit.LastCompileMethod=Unknown
MetaHumanSpeech2Face.TimeStamp=2025.02.12-07.26.44
MetaHumanSpeech2Face.LastCompileMethod=Unknown
MetaHumanBatchProcessor.TimeStamp=2025.02.12-07.26.43
MetaHumanBatchProcessor.LastCompileMethod=Unknown
MetaHumanConfig.TimeStamp=2025.02.12-07.26.43
MetaHumanConfig.LastCompileMethod=Unknown
MetaHumanConfigEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanConfigEditor.LastCompileMethod=Unknown
MetaHumanFaceAnimationSolver.TimeStamp=2025.02.12-07.26.43
MetaHumanFaceAnimationSolver.LastCompileMethod=Unknown
MetaHumanFaceAnimationSolverEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanFaceAnimationSolverEditor.LastCompileMethod=Unknown
MetaHumanFaceFittingSolver.TimeStamp=2025.02.12-07.26.43
MetaHumanFaceFittingSolver.LastCompileMethod=Unknown
MetaHumanFaceFittingSolverEditor.TimeStamp=2025.02.12-07.26.43
MetaHumanFaceFittingSolverEditor.LastCompileMethod=Unknown
MetaHumanPlatform.TimeStamp=2025.02.12-07.26.44
MetaHumanPlatform.LastCompileMethod=Unknown
MetaHumanControlsConversionTest.TimeStamp=2025.02.12-07.26.43
MetaHumanControlsConversionTest.LastCompileMethod=Unknown
DNAInterchange.TimeStamp=2025.02.12-07.26.43
DNAInterchange.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.03.24-15.07.54
TemplateSequence.LastCompileMethod=Unknown
NNEDenoiser.TimeStamp=2025.03.24-15.07.54
NNEDenoiser.LastCompileMethod=Unknown
SequencerScripting.TimeStamp=2025.03.24-15.07.54
SequencerScripting.LastCompileMethod=Unknown
SequencerScriptingEditor.TimeStamp=2025.03.24-15.07.54
SequencerScriptingEditor.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.03.24-15.08.08
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.03.24-15.08.08
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
AndroidPermission.TimeStamp=2025.03.24-15.08.08
AndroidPermission.LastCompileMethod=Unknown
AppleImageUtils.TimeStamp=2025.03.24-15.08.08
AppleImageUtils.LastCompileMethod=Unknown
AppleImageUtilsBlueprintSupport.TimeStamp=2025.03.24-15.08.08
AppleImageUtilsBlueprintSupport.LastCompileMethod=Unknown
AudioCapture.TimeStamp=2025.03.24-15.08.08
AudioCapture.LastCompileMethod=Unknown
AudioCaptureWasapi.TimeStamp=2025.03.24-15.01.56
AudioCaptureWasapi.LastCompileMethod=Unknown
ArchVisCharacter.TimeStamp=2025.03.24-15.08.08
ArchVisCharacter.LastCompileMethod=Unknown
AudioWidgetsEditor.TimeStamp=2025.03.24-15.08.09
AudioWidgetsEditor.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.03.24-15.08.08
AssetTags.LastCompileMethod=Unknown
ComputeFrameworkEditor.TimeStamp=2025.03.24-15.08.13
ComputeFrameworkEditor.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.03.24-15.08.14
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.03.24-15.08.14
DynamicMesh.LastCompileMethod=Unknown
MeshFileUtils.TimeStamp=2025.03.24-15.08.14
MeshFileUtils.LastCompileMethod=Unknown
HairStrandsDeformer.TimeStamp=2025.03.24-15.08.17
HairStrandsDeformer.LastCompileMethod=Unknown
HairStrandsRuntime.TimeStamp=2025.03.24-15.08.17
HairStrandsRuntime.LastCompileMethod=Unknown
HairStrandsEditor.TimeStamp=2025.03.24-15.08.17
HairStrandsEditor.LastCompileMethod=Unknown
HairCardGeneratorFramework.TimeStamp=2025.03.24-15.08.17
HairCardGeneratorFramework.LastCompileMethod=Unknown
InputDebugging.TimeStamp=2025.03.24-15.08.19
InputDebugging.LastCompileMethod=Unknown
InputDebuggingEditor.TimeStamp=2025.03.24-15.08.19
InputDebuggingEditor.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.03.24-15.08.14
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.03.24-15.08.14
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.03.24-15.08.14
GeometryCacheTracks.LastCompileMethod=Unknown
GooglePAD.TimeStamp=2025.03.24-15.08.17
GooglePAD.LastCompileMethod=Unknown
MeshModelingTools.TimeStamp=2025.03.24-15.08.19
MeshModelingTools.LastCompileMethod=Unknown
MeshModelingToolsEditorOnly.TimeStamp=2025.03.24-15.08.19
MeshModelingToolsEditorOnly.LastCompileMethod=Unknown
ModelingComponents.TimeStamp=2025.03.24-15.08.19
ModelingComponents.LastCompileMethod=Unknown
GeometryFramework.TimeStamp=2025.03.24-15.02.03
GeometryFramework.LastCompileMethod=Unknown
ModelingComponentsEditorOnly.TimeStamp=2025.03.24-15.08.19
ModelingComponentsEditorOnly.LastCompileMethod=Unknown
ModelingOperators.TimeStamp=2025.03.24-15.08.19
ModelingOperators.LastCompileMethod=Unknown
ModelingOperatorsEditorOnly.TimeStamp=2025.03.24-15.08.19
ModelingOperatorsEditorOnly.LastCompileMethod=Unknown
MobilePatchingUtils.TimeStamp=2025.03.24-15.08.23
MobilePatchingUtils.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.03.24-15.08.28
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
SkeletalMerging.TimeStamp=2025.03.24-15.08.30
SkeletalMerging.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.03.24-15.08.30
SynthesisEditor.LastCompileMethod=Unknown
StateTreeEditorModule.TimeStamp=2025.03.24-15.08.30
StateTreeEditorModule.LastCompileMethod=Unknown
UnrealUSDWrapper.TimeStamp=2025.03.24-15.08.30
UnrealUSDWrapper.LastCompileMethod=Unknown
USDUtilities.TimeStamp=2025.03.24-15.08.30
USDUtilities.LastCompileMethod=Unknown
USDClasses.TimeStamp=2025.03.24-15.08.30
USDClasses.LastCompileMethod=Unknown
SkeletalMeshModelingTools.TimeStamp=2025.03.24-15.07.13
SkeletalMeshModelingTools.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.03.24-15.02.11
SkeletalMeshEditor.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.03.24-15.06.57
ContentBrowserFileDataSource.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.03.24-15.06.57
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.03.24-15.01.58
CollectionManager.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.03.24-15.06.57
ContentBrowserClassDataSource.LastCompileMethod=Unknown
PortableObjectFileDataSource.TimeStamp=2025.03.24-15.06.57
PortableObjectFileDataSource.LastCompileMethod=Unknown
LightMixer.TimeStamp=2025.03.24-15.06.57
LightMixer.LastCompileMethod=Unknown
ObjectMixerEditor.TimeStamp=2025.03.24-15.06.57
ObjectMixerEditor.LastCompileMethod=Unknown
MetaHumanSDKEditor.TimeStamp=2025.03.24-15.07.21
MetaHumanSDKEditor.LastCompileMethod=Unknown
MetaHumanSDKRuntime.TimeStamp=2025.03.24-15.07.21
MetaHumanSDKRuntime.LastCompileMethod=Unknown
BaseCharacterFXEditor.TimeStamp=2025.03.24-15.07.13
BaseCharacterFXEditor.LastCompileMethod=Unknown
RiderBlueprint.TimeStamp=2025.05.12-16.50.12
RiderBlueprint.LastCompileMethod=Unknown
RiderGameControl.TimeStamp=2025.05.12-16.50.14
RiderGameControl.LastCompileMethod=Unknown
XInputDevice.TimeStamp=2025.03.24-15.08.30
XInputDevice.LastCompileMethod=Unknown
ConcertSyncClient.TimeStamp=2025.03.24-15.06.55
ConcertSyncClient.LastCompileMethod=Unknown
BlenderLink.TimeStamp=2025.05.22-18.27.45
BlenderLink.LastCompileMethod=External
Bridge.TimeStamp=2024.12.26-21.35.25
Bridge.LastCompileMethod=Unknown
MegascansPlugin.TimeStamp=2024.12.26-21.35.26
MegascansPlugin.LastCompileMethod=Unknown
CmdLinkServer.TimeStamp=2025.03.24-15.06.55
CmdLinkServer.LastCompileMethod=Unknown
Fab.TimeStamp=2025.02.17-15.51.43
Fab.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.03.24-15.08.40
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.03.24-15.08.41
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.03.24-15.08.40
TakeRecorderSources.LastCompileMethod=Unknown
CacheTrackRecorder.TimeStamp=2025.03.24-15.08.40
CacheTrackRecorder.LastCompileMethod=Unknown
DataflowEditor.TimeStamp=2025.03.24-15.07.20
DataflowEditor.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.03.24-15.08.09
AudioSynesthesiaEditor.LastCompileMethod=Unknown
ProfileVisualizer.TimeStamp=2025.03.24-15.02.09
ProfileVisualizer.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.03.24-15.02.05
ImageWriteQueue.LastCompileMethod=Unknown
TypedElementRuntime.TimeStamp=2025.03.24-15.02.14
TypedElementRuntime.LastCompileMethod=Unknown
LevelInstanceEditor.TimeStamp=2025.03.24-15.02.05
LevelInstanceEditor.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.03.24-15.01.55
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.03.24-15.02.07
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.03.24-15.01.55
AITestSuite.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.03.24-15.02.03
GameplayDebugger.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.03.24-15.02.07
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.03.24-15.02.09
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.03.24-15.02.09
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.03.24-15.01.55
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.03.24-15.02.05
LauncherPlatform.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.03.24-15.01.56
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixer.TimeStamp=2025.03.24-15.01.56
AudioMixer.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.03.24-15.01.56
AudioMixerCore.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.03.24-15.02.14
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.03.24-15.02.07
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.03.24-15.02.07
MovieSceneTracks.LastCompileMethod=Unknown
CinematicCamera.TimeStamp=2025.03.24-15.01.58
CinematicCamera.LastCompileMethod=Unknown
SparseVolumeTexture.TimeStamp=2025.03.24-15.02.11
SparseVolumeTexture.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.03.24-15.01.59
Documentation.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.03.24-15.02.07
OutputLog.LastCompileMethod=Unknown
SourceControlWindows.TimeStamp=2025.03.24-15.02.11
SourceControlWindows.LastCompileMethod=Unknown
SourceControlWindowExtender.TimeStamp=2025.03.24-15.02.11
SourceControlWindowExtender.LastCompileMethod=Unknown
UncontrolledChangelists.TimeStamp=2025.03.24-15.02.15
UncontrolledChangelists.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.03.24-15.01.58
ClassViewer.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.03.24-15.02.14
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.03.24-15.02.03
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.03.24-15.02.05
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.03.24-15.02.05
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.03.24-15.02.08
Persona.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.03.24-15.01.55
AdvancedPreviewScene.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.03.24-15.01.55
AnimationBlueprintEditor.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.03.24-15.02.07
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.03.24-15.01.59
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.03.24-15.01.58
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.03.24-15.02.05
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.03.24-15.01.56
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.03.24-15.01.56
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.03.24-15.01.59
DeviceManager.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.03.24-15.02.14
TargetDeviceServices.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.03.24-15.02.11
SessionFrontend.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.03.24-15.02.11
ProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.03.24-15.02.11
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.03.24-15.01.59
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.03.24-15.02.05
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.03.24-15.02.11
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.03.24-15.02.11
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.03.24-15.01.57
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.03.24-15.02.18
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.03.24-15.02.15
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.03.24-15.01.59
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.03.24-15.02.03
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.03.24-15.02.06
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.03.24-15.02.06
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.03.24-15.02.07
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.03.24-15.02.05
InputBindingEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.03.24-15.01.59
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
InteractiveToolsFramework.TimeStamp=2025.03.24-15.02.05
InteractiveToolsFramework.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.03.24-15.02.14
TraceInsights.LastCompileMethod=Unknown
TraceInsightsCore.TimeStamp=2025.03.24-15.02.14
TraceInsightsCore.LastCompileMethod=Unknown
StaticMeshEditor.TimeStamp=2025.03.24-15.02.14
StaticMeshEditor.LastCompileMethod=Unknown
EditorFramework.TimeStamp=2025.03.24-15.01.59
EditorFramework.LastCompileMethod=Unknown
EditorConfig.TimeStamp=2025.03.24-15.01.59
EditorConfig.LastCompileMethod=Unknown
DerivedDataEditor.TimeStamp=2025.03.24-15.01.59
DerivedDataEditor.LastCompileMethod=Unknown
CSVtoSVG.TimeStamp=2025.03.24-15.01.58
CSVtoSVG.LastCompileMethod=Unknown
VirtualizationEditor.TimeStamp=2025.03.24-15.02.16
VirtualizationEditor.LastCompileMethod=Unknown
AnimationSettings.TimeStamp=2025.03.24-15.01.55
AnimationSettings.LastCompileMethod=Unknown
GameplayDebuggerEditor.TimeStamp=2025.03.24-15.02.03
GameplayDebuggerEditor.LastCompileMethod=Unknown
RenderResourceViewer.TimeStamp=2025.03.24-15.02.11
RenderResourceViewer.LastCompileMethod=Unknown
UniversalObjectLocatorEditor.TimeStamp=2025.03.24-15.02.15
UniversalObjectLocatorEditor.LastCompileMethod=Unknown
StructUtilsEditor.TimeStamp=2025.03.24-15.02.14
StructUtilsEditor.LastCompileMethod=Unknown
StructUtilsTestSuite.TimeStamp=2025.03.24-15.02.14
StructUtilsTestSuite.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.03.24-15.01.37
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.03.24-15.01.45
IOSRuntimeSettings.LastCompileMethod=Unknown
MacPlatformEditor.TimeStamp=2025.03.24-15.02.06
MacPlatformEditor.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.03.24-15.02.18
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.03.24-15.01.37
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.03.24-15.01.37
AndroidDeviceDetection.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.03.24-15.02.08
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.03.24-15.01.45
IOSPlatformEditor.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.03.24-15.02.06
LogVisualizer.LastCompileMethod=Unknown
WidgetRegistration.TimeStamp=2025.03.24-15.02.16
WidgetRegistration.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.03.24-15.01.58
ClothPainter.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.03.24-15.02.16
ViewportInteraction.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.03.24-15.02.00
EditorWidgets.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.03.24-15.02.16
ViewportSnapping.LastCompileMethod=Unknown
MeshPaint.TimeStamp=2025.03.24-15.02.07
MeshPaint.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.03.24-15.02.08
PlacementMode.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.03.24-15.02.11
SessionServices.LastCompileMethod=Unknown
AndroidMediaEditor.TimeStamp=2025.03.24-15.07.48
AndroidMediaEditor.LastCompileMethod=Unknown
AndroidMediaFactory.TimeStamp=2025.03.24-15.07.48
AndroidMediaFactory.LastCompileMethod=Unknown
AvfMediaEditor.TimeStamp=2025.03.24-15.07.48
AvfMediaEditor.LastCompileMethod=Unknown
AvfMediaFactory.TimeStamp=2025.03.24-15.07.48
AvfMediaFactory.LastCompileMethod=Unknown
ImgMediaEditor.TimeStamp=2025.03.24-15.07.51
ImgMediaEditor.LastCompileMethod=Unknown
ImgMediaFactory.TimeStamp=2025.03.24-15.07.51
ImgMediaFactory.LastCompileMethod=Unknown
OpenExrWrapper.TimeStamp=2025.03.24-15.07.51
OpenExrWrapper.LastCompileMethod=Unknown
MediaCompositingEditor.TimeStamp=2025.03.24-15.07.51
MediaCompositingEditor.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.03.24-15.02.11
SequenceRecorder.LastCompileMethod=Unknown
MediaIOEditor.TimeStamp=2025.03.24-15.07.51
MediaIOEditor.LastCompileMethod=Unknown
MediaPlayerEditor.TimeStamp=2025.03.24-15.07.51
MediaPlayerEditor.LastCompileMethod=Unknown
WebMMedia.TimeStamp=2025.03.24-15.07.54
WebMMedia.LastCompileMethod=Unknown
WebMMediaEditor.TimeStamp=2025.03.24-15.07.54
WebMMediaEditor.LastCompileMethod=Unknown
WebMMediaFactory.TimeStamp=2025.03.24-15.07.54
WebMMediaFactory.LastCompileMethod=Unknown
WmfMediaEditor.TimeStamp=2025.03.24-15.07.54
WmfMediaEditor.LastCompileMethod=Unknown
WmfMediaFactory.TimeStamp=2025.03.24-15.07.54
WmfMediaFactory.LastCompileMethod=Unknown
SmartSnapping.TimeStamp=2025.03.24-15.06.41
SmartSnapping.LastCompileMethod=Unknown
ControlRigEditor.TimeStamp=2025.03.24-15.06.42
ControlRigEditor.LastCompileMethod=Unknown
OptimusEditor.TimeStamp=2025.03.24-15.06.52
OptimusEditor.LastCompileMethod=Unknown
IKRigEditor.TimeStamp=2025.03.24-15.06.52
IKRigEditor.LastCompileMethod=Unknown
LiveLinkMultiUser.TimeStamp=2025.03.24-15.06.52
LiveLinkMultiUser.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.03.24-15.06.52
CameraShakePreviewer.LastCompileMethod=Unknown
GameplayCamerasEditor.TimeStamp=2025.03.24-15.06.55
GameplayCamerasEditor.LastCompileMethod=Unknown
EngineAssetDefinitions.TimeStamp=2025.03.24-15.06.57
EngineAssetDefinitions.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.03.24-15.06.57
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.03.24-15.06.57
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.03.24-15.06.57
TextureAlignMode.LastCompileMethod=Unknown
CharacterAI.TimeStamp=2025.03.24-15.07.13
CharacterAI.LastCompileMethod=Unknown
FractureEngine.TimeStamp=2025.03.24-15.07.20
FractureEngine.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.03.24-15.07.32
PlanarCut.LastCompileMethod=Unknown
AutoRigService.TimeStamp=2025.02.12-07.26.42
AutoRigService.LastCompileMethod=Unknown
MetaHumanPerformance.TimeStamp=2025.02.12-07.26.43
MetaHumanPerformance.LastCompileMethod=Unknown
MetaHumanSequencer.TimeStamp=2025.02.12-07.26.44
MetaHumanSequencer.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.03.24-15.07.54
ActorSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.03.24-15.07.54
TemplateSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.03.24-15.07.54
LevelSequenceEditor.LastCompileMethod=Unknown
AndroidFileServerEditor.TimeStamp=2025.03.24-15.08.08
AndroidFileServerEditor.LastCompileMethod=Unknown
AudioCaptureEditor.TimeStamp=2025.03.24-15.08.08
AudioCaptureEditor.LastCompileMethod=Unknown
GooglePADEditor.TimeStamp=2025.03.24-15.08.17
GooglePADEditor.LastCompileMethod=Unknown
ResonanceAudioEditor.TimeStamp=2025.03.24-15.08.29
ResonanceAudioEditor.LastCompileMethod=Unknown
RigVMEditor.TimeStamp=2025.03.24-15.08.29
RigVMEditor.LastCompileMethod=Unknown
WaveTableEditor.TimeStamp=2025.03.24-15.08.30
WaveTableEditor.LastCompileMethod=Unknown
RiderShaderInfo.TimeStamp=2025.05.12-16.50.00
RiderShaderInfo.LastCompileMethod=Unknown
RiderLC.TimeStamp=2025.05.12-16.50.01
RiderLC.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.03.24-15.01.55
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.03.24-15.02.11
SceneDepthPickerMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.03.24-15.02.05
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.03.24-15.02.03
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.03.24-15.02.16
VirtualTexturingEditor.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.03.24-15.01.56
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=2025.03.24-15.02.11
SequenceRecorderSections.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.03.24-15.02.14
StatsViewer.LastCompileMethod=Unknown
DataLayerEditor.TimeStamp=2025.03.24-15.01.59
DataLayerEditor.LastCompileMethod=Unknown
AndroidDeviceProfileSelector.TimeStamp=2025.03.24-15.08.08
AndroidDeviceProfileSelector.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.03.24-15.02.03
GameProjectGeneration.LastCompileMethod=Unknown
UnsavedAssetsTracker.TimeStamp=2025.03.24-15.02.16
UnsavedAssetsTracker.LastCompileMethod=Unknown
StatusBar.TimeStamp=2025.03.24-15.02.14
StatusBar.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.03.24-15.01.55
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.03.24-15.02.16
WidgetCarousel.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.03.24-15.02.11
SceneOutliner.LastCompileMethod=Unknown
SubobjectEditor.TimeStamp=2025.03.24-15.02.14
SubobjectEditor.LastCompileMethod=Unknown
WebBrowser.TimeStamp=2025.03.24-15.02.16
WebBrowser.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.03.24-15.02.03
HierarchicalLODOutliner.LastCompileMethod=Unknown
SkeletonEditor.TimeStamp=2025.03.24-15.02.11
SkeletonEditor.LastCompileMethod=Unknown
PinnedCommandList.TimeStamp=2025.03.24-15.02.08
PinnedCommandList.LastCompileMethod=Unknown
ClothingSystemEditorInterface.TimeStamp=2025.03.24-15.01.58
ClothingSystemEditorInterface.LastCompileMethod=Unknown
AppleARKit.TimeStamp=2025.03.24-15.08.08
AppleARKit.LastCompileMethod=Unknown
AugmentedReality.TimeStamp=2025.03.24-15.01.56
AugmentedReality.LastCompileMethod=Unknown
AppleARKitPoseTrackingLiveLink.TimeStamp=2025.03.24-15.08.08
AppleARKitPoseTrackingLiveLink.LastCompileMethod=Unknown
AppleARKitFaceSupport.TimeStamp=2025.03.24-15.08.08
AppleARKitFaceSupport.LastCompileMethod=Unknown
LiveLinkControlRig.TimeStamp=2025.03.24-15.07.21
LiveLinkControlRig.LastCompileMethod=Unknown
XRBase.TimeStamp=2025.03.24-15.08.31
XRBase.LastCompileMethod=Unknown
XRBaseEditor.TimeStamp=2025.03.24-15.08.31
XRBaseEditor.LastCompileMethod=Unknown
ARUtilities.TimeStamp=2025.03.24-15.08.08
ARUtilities.LastCompileMethod=Unknown
ExternalImagePicker.TimeStamp=2025.03.24-15.02.03
ExternalImagePicker.LastCompileMethod=Unknown
LauncherServices.TimeStamp=2025.03.24-15.02.05
LauncherServices.LastCompileMethod=Unknown
SequencerWidgets.TimeStamp=2025.03.24-15.02.11
SequencerWidgets.LastCompileMethod=Unknown
PhysicsAssetEditor.TimeStamp=2025.03.24-15.02.08
PhysicsAssetEditor.LastCompileMethod=Unknown
AnimationEditor.TimeStamp=2025.03.24-15.01.55
AnimationEditor.LastCompileMethod=Unknown

[PluginBrowser]
InstalledPlugins=NDIMedia
InstalledPlugins=MetaHuman
InstalledPlugins=WMFCodecs

[AssetEditorSubsystem]
CleanShutdown=True
DebuggerAttached=False
RecentAssetEditors=LevelSequenceEditor
RecentAssetEditors=SkeletalMeshEditor
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
RecentAssetEditors=
OpenAssetsAtExit=/Game/MetaHumans/Test/Test.Test

[DetailCategories]
NewPluginDescriptorData.Descriptor Data=True
StaticMeshActor.TransformCommon=True
StaticMeshActor.StaticMesh=True
StaticMeshActor.Materials=True
StaticMeshActor.Physics=True
StaticMeshActor.Collision=True
StaticMeshActor.Lighting=True
StaticMeshActor.Rendering=True
StaticMeshActor.Mesh Painting=True
StaticMeshActor.HLOD=True
StaticMeshActor.Navigation=True
StaticMeshActor.VirtualTexture=True
StaticMeshActor.Tags=True
StaticMeshActor.Cooking=True
StaticMeshActor.Replication=True
StaticMeshActor.Networking=True
StaticMeshActor.Actor=True
LandscapeStreamingProxy.TransformCommon=True
LandscapeStreamingProxy.Information=True
LandscapeStreamingProxy.LandscapeProxy=True
LandscapeStreamingProxy.Landscape=True
LandscapeStreamingProxy.Nanite=True
LandscapeStreamingProxy.LOD=True
LandscapeStreamingProxy.LOD Distribution=True
LandscapeStreamingProxy.Lighting=True
LandscapeStreamingProxy.VirtualTexture=True
LandscapeStreamingProxy.Rendering=True
LandscapeStreamingProxy.Lightmass=True
LandscapeStreamingProxy.Collision=True
LandscapeStreamingProxy.Navigation=True
LandscapeStreamingProxy.HLOD=True
LandscapeStreamingProxy.Target Layers=True
LandscapeStreamingProxy.Replication=True
LandscapeStreamingProxy.Networking=True
LandscapeStreamingProxy.Input=True
LandscapeStreamingProxy.Actor=True
BP_MH_Friend_C.TransformCommon=True
BP_MH_Friend_C.Live Link=True
BP_MH_Friend_C.LiveRetarget=True
BP_MH_Friend_C.Rendering=True
BP_MH_Friend_C.Replication=True
BP_MH_Friend_C.Collision=True
BP_MH_Friend_C.HLOD=True
BP_MH_Friend_C.Physics=True
BP_MH_Friend_C.Networking=True
BP_MH_Friend_C.Input=True
BP_MH_Friend_C.Actor=True
DNAAssetImportUI.Mesh=True
DNAAssetImportUI.DNAFileInformation=True
ClothingAssetCommon.Config=True
ClothingAssetCommon.Import=True
PersonaPreviewSceneDescription.Animation=True
AssetViewerSettings.Settings=True
Texture2D.LevelOfDetail=True
Texture2D.Compression=True
Texture2D.Texture=True
Texture2D.Adjustments=True
Texture2D.File Path=True
Texture2D.Compositing=True
InterchangeGenericAssetsPipeline.Common=True
InterchangeGenericAssetsPipeline.Common Meshes=True
InterchangeGenericAssetsPipeline.Common Skeletal Meshes and Animations=True
InterchangeGenericAssetsPipeline.Static Meshes=True
InterchangeGenericAssetsPipeline.Skeletal Meshes=True
InterchangeGenericAssetsPipeline.Animations=True
InterchangeGenericAssetsPipeline.Materials=True
InterchangeGenericAssetsPipeline.Textures=True
InterchangeGenericAssetsPipeline.Extra Information=True
SkeletalMesh.Material Slots=True
SkeletalMesh.LODCustomMode=True
SkeletalMesh.LOD0=True
SkeletalMesh.LODSettings=True
SkeletalMesh.Clothing=True
SkeletalMesh.SkeletalMesh=True
SkeletalMesh.Mesh=True
SkeletalMesh.SkinWeights=True
SkeletalMesh.ImportSettings=True
SkeletalMesh.AnimationRig=True
SkeletalMesh.Physics=True
SkeletalMesh.Lighting=True
SkeletalMesh.Animation=True
SkeletalMesh.RayTracing=True
SkeletalMesh.Sampling=True
SkeletalMesh.Deformer=True
SkeletalMesh.Rendering=True
MaterialEditorInstanceConstant.ParameterGroups=True
MaterialEditorInstanceConstant.General=True
MaterialEditorInstanceConstant.Previewing=True
StaticMesh.StaticMeshMaterials=True
StaticMesh.NaniteSettings=True
StaticMesh.LODCustomMode=True
StaticMesh.LOD0=True
StaticMesh.LodSettings=True
StaticMesh.StaticMesh=True
StaticMesh.Collision=True
StaticMesh.ImportSettings=True
StaticMesh.RayTracing=True
StaticMesh.Navigation=True
SkeletalMeshActor.TransformCommon=True
SkeletalMeshActor.Animation=True
SkeletalMeshActor.Mesh=True
SkeletalMeshActor.Physics=True
SkeletalMeshActor.Collision=True
SkeletalMeshActor.Clothing=True
SkeletalMeshActor.LeaderPoseComponent=True
SkeletalMeshActor.Lighting=True
SkeletalMeshActor.AnimationRig=True
SkeletalMeshActor.Deformer=True
SkeletalMeshActor.SkinWeights=True
SkeletalMeshActor.Rendering=True
SkeletalMeshActor.HLOD=True
SkeletalMeshActor.Navigation=True
SkeletalMeshActor.VirtualTexture=True
SkeletalMeshActor.Tags=True
SkeletalMeshActor.Activation=True
SkeletalMeshActor.Cooking=True
SkeletalMeshActor.Replication=True
SkeletalMeshActor.Networking=True
SkeletalMeshActor.Input=True
SkeletalMeshActor.Actor=True
SkeletalMeshActor.Materials=True
LevelSequenceActor.TransformCommon=True
LevelSequenceActor.General=True
LevelSequenceActor.Playback=True
LevelSequenceActor.Cinematic=True
LevelSequenceActor.BurnInOptions=True
LevelSequenceActor.Aspect Ratio=True
LevelSequenceActor.Replication=True
LevelSequenceActor.BindingOverrides=True
LevelSequenceActor.InstanceData=True
LevelSequenceActor.Collision=True
LevelSequenceActor.Networking=True
LevelSequenceActor.Actor=True
SkyLight.TransformCommon=True
SkyLight.Light=True
SkyLight.Rendering=True
SkyLight.DistanceFieldAmbientOcclusion=True
SkyLight.AtmosphereAndCloud=True
SkyLight.RayTracing=True
SkyLight.SkyLight=True
SkyLight.Tags=True
SkyLight.Cooking=True
SkyLight.Physics=True
SkyLight.Networking=True
SkyLight.Actor=True
SkyAtmosphere.TransformCommon=True
SkyAtmosphere.Planet=True
SkyAtmosphere.Atmosphere=True
SkyAtmosphere.Atmosphere - Rayleigh=True
SkyAtmosphere.Atmosphere - Mie=True
SkyAtmosphere.Atmosphere - Absorption=True
SkyAtmosphere.Art Direction=True
SkyAtmosphere.Rendering=True
SkyAtmosphere.Physics=True
SkyAtmosphere.Tags=True
SkyAtmosphere.Activation=True
SkyAtmosphere.Cooking=True
SkyAtmosphere.Replication=True
SkyAtmosphere.Networking=True
SkyAtmosphere.Actor=True
ControlRigEditModeSettings.Animation Settings=True
AnimDetailControlsProxyTransform.Transform=True
AnimDetailControlsProxyFloat.Float=True
ControlRigPoseMirrorSettings.Mirror Settings=True
GeneralProjectSettings.About=True
GeneralProjectSettings.Publisher=True
GeneralProjectSettings.Legal=True
GeneralProjectSettings.Displayed=True
GeneralProjectSettings.Settings=True
CryptoKeysSettings.Encryption=True
CryptoKeysSettings.Signing=True
GameplayTagsSettings.GameplayTags=True
GameplayTagsSettings.Advanced Gameplay Tags=True
GameplayTagsSettings.Advanced Replication=True
GameMapsSettings.DefaultModes=True
GameMapsSettings.DefaultMaps=True
GameMapsSettings.LocalMultiplayer=True
GameMapsSettings.GameInstance=True
MoviePlayerSettings.Movies=True
ProjectPackagingSettings.CustomBuilds=True
ProjectPackagingSettings.Packaging=True
ProjectPackagingSettings.Project=True
ProjectPackagingSettings.Prerequisites=True
HardwareTargetingSettings.Target Hardware=True
HardwareTargetingSettings.Pending Changes=True
AssetManagerSettings.Asset Manager=True
AssetManagerSettings.Redirects=True
AssetToolsSettings.Advanced Copy=True
SlateRHIRendererSettings.PostProcessing=True
AISystem.AISystem=True
AISystem.Movement=True
AISystem.EQS=True
AISystem.Blackboard=True
AISystem.Behavior Tree=True
AISystem.PerceptionSystem=True
AnimationSettings.Compression=True
AnimationSettings.Performance=True
AnimationSettings.AnimationAttributes=True
AnimationSettings.Mirroring=True
AnimationSettings.AnimationData=True
AnimationModifierSettings.Modifiers=True
AudioSettings.Dialogue=True
AudioSettings.Audio=True
AudioSettings.Mix=True
AudioSettings.Quality=True
AudioSettings.Debug=True
ChaosSolverSettings.GameInstance=True
CineCameraSettings.Lens=True
CineCameraSettings.Filmback=True
CineCameraSettings.Crop=True
CollisionProfile.Object Channels=True
CollisionProfile.Trace Channels=True
ConsoleSettings.General=True
ConsoleSettings.AutoComplete=True
ConsoleSettings.Colors=True
ControlRigSettings.Shapes=True
ControlRigSettings.ModularRigging=True
CookerSettings.Cooker=True
CookerSettings.Textures=True
CookerSettings.Editor=True
CrowdManager.Config=True
DataDrivenConsoleVariableSettings.DataDrivenCVar=True
DebugCameraControllerSettings.General=True
OptimusSettings.DeformerGraph=True
EnhancedInputDeveloperSettings.Enhanced Input=True
InputModifierSmoothDelta.Settings=True
InputModifierDeadZone.Settings=True
InputModifierResponseCurveExponential.Settings=True
InputModifierFOVScaling.Settings=True
EnhancedInputDeveloperSettings.Modifier Default Values=True
InputTriggerDown.Trigger Settings=True
InputTriggerPressed.Trigger Settings=True
InputTriggerReleased.Trigger Settings=True
InputTriggerHold.Trigger Settings=True
InputTriggerHoldAndRelease.Trigger Settings=True
InputTriggerTap.Trigger Settings=True
InputTriggerPulse.Trigger Settings=True
EnhancedInputDeveloperSettings.Trigger Default Values=True
EnhancedInputEditorProjectSettings.Default=True
MegascansMaterialParentSettings.Parent Materials=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
InterchangeFbxSettings.FBX=True
GameplayDebuggerConfig.Input=True
GameplayDebuggerConfig.Display=True
GameplayDebuggerConfig.AddOns=True
GarbageCollectionSettings.General=True
GarbageCollectionSettings.Optimization=True
GarbageCollectionSettings.Debug=True
Engine.Fonts=True
Engine.DefaultClasses=True
Engine.DefaultMaterials=True
Engine.Settings=True
Engine.Subtitles=True
Engine.Blueprints=True
Engine.Anim Blueprints=True
Engine.Framerate=True
Engine.Timecode=True
Engine.Screenshots=True
GLTFPipelineSettings.PredefinedglTFMaterialLibrary=True
HierarchicalLODSettings.HLODSystem=True
InputSettings.Bindings=True
InputSettings.Platforms=True
InputSettings.ViewportProperties=True
InputSettings.Input=True
InputSettings.Mobile=True
InputSettings.Virtual Keyboard (Mobile)=True
InputSettings.DefaultClasses=True
InputSettings.Console=True
InterchangeProjectSettings.ImportContent=True
InterchangeProjectSettings.ImportIntoLevel=True
InterchangeProjectSettings.EditorInterface=True
InterchangeProjectSettings.Generic=True
InterchangeProjectSettings.Editor Generic Pipeline Class=True
InterchangeProjectSettings.Converters=True
InterchangeProjectSettings.Groups=True
LandscapeSettings.Layers=True
LandscapeSettings.Configuration=True
LandscapeSettings.Materials=True
LandscapeSettings.HLOD=True
LevelSequenceProjectSettings.Timeline=True
MassSettings.Mass=True
MaterialXPipelineSettings.MaterialXPredefined . Surface Shaders=True
MaterialXPipelineSettings.MaterialXPredefined . BSDF=True
MaterialXPipelineSettings.MaterialXPredefined . EDF=True
MaterialXPipelineSettings.MaterialXPredefined . VDF=True
MeshBudgetProjectSettings.StaticMesh=True
MeshDrawCommandStatsSettings.Engine=True
MetaSoundSettings.AutoUpdate=True
MetaSoundSettings.Registration=True
MetaSoundSettings.Pages (Experimental)=True
MetaSoundSettings.Quality=True
RecastNavMesh.Display=True
RecastNavMesh.Generation=True
RecastNavMesh.Query=True
RecastNavMesh.Runtime=True
RecastNavMesh.Tick=True
RecastNavMesh.Collision=True
RecastNavMesh.HLOD=True
RecastNavMesh.Physics=True
RecastNavMesh.Networking=True
NavigationSystemV1.Navigation=True
NavigationSystemV1.NavigationSystem=True
NavigationSystemV1.Navigation Enforcing=True
NavigationSystemV1.Agents=True
NetworkSettings.libcurl=True
NetworkSettings.World=True
ObjectMixerEditorSettings.Object Mixer=True
PhysicsSettings.Replication=True
PhysicsSettings.Simulation=True
PhysicsSettings.Optimization=True
PhysicsSettings.Framerate=True
PhysicsSettings.Broadphase=True
PhysicsSettings.ChaosPhysics=True
PhysicsSettings.Constants=True
PhysicsSettings.Physical Surface=True
RendererSettings.Mobile=True
RendererSettings.Materials=True
RendererSettings.Culling=True
RendererSettings.Textures=True
RendererSettings.VirtualTextures=True
RendererSettings.Runtime Virtual Textures=True
RendererSettings.WorkingColorSpace=True
RendererSettings.GlobalIllumination=True
RendererSettings.Reflections=True
RendererSettings.Lumen=True
RendererSettings.DirectLighting=True
RendererSettings.HardwareRayTracing=True
RendererSettings.SoftwareRayTracing=True
RendererSettings.Nanite=True
RendererSettings.MiscLighting=True
RendererSettings.ForwardRenderer=True
RendererSettings.Translucency=True
RendererSettings.VR=True
RendererSettings.Postprocessing=True
RendererSettings.DefaultSettings=True
RendererSettings.DefaultScreenPercentage=True
RendererSettings.Optimizations=True
RendererSettings.LightFunctionAtlas=True
RendererSettings.Debugging=True
RendererSettings.Mesh Streaming=True
RendererSettings.Heterogeneous Volumes=True
RendererSettings.Editor=True
RendererSettings.ShaderPermutationReduction=True
RendererSettings.Substrate=True
RendererSettings.HairStrands=True
RendererSettings.MobileShaderPermutationReduction=True
RendererSettings.Skinning=True
RendererSettings.PostProcessCalibrationMaterials=True
RendererOverrideSettings.ShaderPermutationReduction=True
SlateSettings.ConstraintCanvas=True
StateTreeSettings.StateTree=True
StreamingSettings.PackageStreaming=True
StreamingSettings.LevelStreaming=True
StreamingSettings.General=True
StreamingSettings.Deprecated Settings=True
TextureEncodingProjectSettings.EncodeSettings=True
TextureEncodingProjectSettings.EncodeSpeedSettings=True
TextureEncodingProjectSettings.EncodeSpeeds=True
UsdProjectSettings.USD=True
UserInterfaceSettings.Focus=True
UserInterfaceSettings.Hardware Cursors=True
UserInterfaceSettings.Software Cursors=True
UserInterfaceSettings.DPI Scaling=True
UserInterfaceSettings.Widgets=True
UserInterfaceSettings.UMG Fonts=True
VirtualTexturePoolConfig.PoolConfig=True
WorldPartitionSettings.WorldPartition=True
LevelEditor2DSettings.General=True
LevelEditor2DSettings.LayerSnapping=True
EditorProjectAppearanceSettings.Units=True
EditorProjectAppearanceSettings.ReferenceViewer=True
EditorProjectAssetSettings.Redirectors=True
EditorProjectAssetSettings.Internationalization=True
BlueprintEditorProjectSettings.Blueprints=True
BlueprintEditorProjectSettings.Actors=True
BlueprintEditorProjectSettings.Experimental=True
BlueprintEditorProjectSettings.Play=True
ClassViewerProjectSettings.ClassVisibilityManagement=True
ContentBrowserCollectionProjectSettings.Collections=True
DataValidationSettings.Data Validation=True
DDCProjectSettings.Warnings=True
EditorUtilityWidgetProjectSettings.Designer=True
EditorUtilityWidgetProjectSettings.Compiler=True
EditorUtilityWidgetProjectSettings.Class Filtering=True
EditorUtilityWidgetProjectSettings.Class Settings=True
ProxyLODMeshSimplificationSettings.General=True
LevelEditorProjectSettings.Editing=True
LevelInstanceEditorSettings.World Partition=True
MovieSceneToolsProjectSettings.Timeline=True
MovieSceneToolsProjectSettings.Shots=True
MovieSceneToolsProjectSettings.TrackSettings=True
MeshSimplificationSettings.General=True
PaperImporterSettings.NewAssetSettings=True
PaperImporterSettings.ImportSettings=True
PaperImporterSettings.MaterialSettings=True
EditorPerformanceProjectSettings.ViewportResolution=True
SourceControlPreferences.SourceControl=True
SourceControlPreferences.Internationalization=True
RigVMProjectSettings.Experimental=True
SkeletalMeshSimplificationSettings.General=True
PlasticSourceControlProjectSettings.Unity Version Control=True
StructViewerProjectSettings.StructVisibilityManagement=True
TextureImportSettings.VirtualTextures=True
TextureImportSettings.ImportSettings=True
UMGEditorProjectSettings.Compiler=True
UMGEditorProjectSettings.Class Filtering=True
UMGEditorProjectSettings.Designer=True
UMGEditorProjectSettings.Class Settings=True
AndroidRuntimeSettings.APK Packaging=True
AndroidRuntimeSettings.App Bundles=True
AndroidRuntimeSettings.Build=True
AndroidRuntimeSettings.Advanced APK Packaging=True
AndroidRuntimeSettings.DistributionSigning=True
AndroidRuntimeSettings.GooglePlayServices=True
AndroidRuntimeSettings.Icons=True
AndroidRuntimeSettings.LaunchImages=True
AndroidRuntimeSettings.Input=True
AndroidRuntimeSettings.GraphicsDebugger=True
AndroidRuntimeSettings.Audio=True
AndroidRuntimeSettings.MultiTextureFormats=True
AndroidRuntimeSettings.TextureFormatPriorities=True
AndroidRuntimeSettings.Misc=True
ShaderPlatformQualitySettings.Forward Rendering Overrides=True
AndroidSDKSettings.SDKConfig=True
IOSRuntimeSettings.Mobile Provision=True
IOSRuntimeSettings.BundleInformation=True
IOSRuntimeSettings.PowerUsage=True
IOSRuntimeSettings.Orientation=True
IOSRuntimeSettings.FileSystem=True
IOSRuntimeSettings.Input=True
IOSRuntimeSettings.Rendering=True
IOSRuntimeSettings.Build=True
IOSRuntimeSettings.Online=True
IOSRuntimeSettings.RequiredIOSIcons=True
IOSRuntimeSettings.OptionalIOSIcons=True
IOSRuntimeSettings.RequiredTVOSAssets=True
IOSRuntimeSettings.OptionalTVOSAssets=True
IOSRuntimeSettings.LaunchScreen=True
IOSRuntimeSettings.Remote Build=True
IOSRuntimeSettings.Audio=True
LinuxTargetSettings.Targeted RHIs=True
LinuxTargetSettings.Splash=True
LinuxTargetSettings.Icon=True
LinuxTargetSettings.Audio=True
MacTargetSettings.Targeted RHIs=True
MacTargetSettings.Rendering=True
MacTargetSettings.Packaging=True
MacTargetSettings.Splash=True
MacTargetSettings.Icon=True
MacTargetSettings.Audio=True
WindowsTargetSettings.D3D12 Targeted Shader Formats=True
WindowsTargetSettings.D3D11 Targeted Shader Formats=True
WindowsTargetSettings.Vulkan Targeted Shader Formats=True
WindowsTargetSettings.Targeted RHIs=True
WindowsTargetSettings.Toolchain=True
WindowsTargetSettings.Splash=True
WindowsTargetSettings.Icon=True
WindowsTargetSettings.Audio=True
XcodeProjectSettings.Xcode=True
XcodeProjectSettings.Plist Files=True
XcodeProjectSettings.Entitlements=True
XcodeProjectSettings.Code Signing=True
XcodeProjectSettings.Privacy Manifests=True
AndroidFileServerRuntimeSettings.Packaging=True
AndroidFileServerRuntimeSettings.Deployment=True
AndroidFileServerRuntimeSettings.Connection=True
AppleARKitSettings.AR Settings=True
AvfMediaSettings.Debug=True
CameraCalibrationSettings.Settings=True
CameraCalibrationSettings.Overlays=True
DataflowSettings.PinColors=True
DataflowSettings.NodeColors=True
FractureModeSettings.Fracture Mode=True
GameplayCamerasSettings.General=True
GameplayCamerasSettings.IK Aiming=True
GameplayCamerasEditorSettings.NodeTitleColors=True
GeometryCacheStreamerSettings.Geometry Cache Streamer=True
GooglePADRuntimeSettings.Packaging=True
GroomPluginSettings.GroomCache=True
HoldoutCompositeSettings.General=True
ImgMediaSettings.General=True
ImgMediaSettings.Caching=True
ImgMediaSettings.EXR=True
ImgMediaSettings.Proxies=True
ToolPresetProjectSettings.Interactive Tool Presets=True
LevelSequenceEditorSettings.Tracks=True
LevelSequenceEditorSettings.Playback=True
LiveLinkSettings.LiveLink=True
LiveLinkComponentSettings.LiveLink=True
LiveLinkSequencerSettings.LiveLink=True
MetaHumanSDKSettings.MetaHuman Import Paths=True
ModelingToolsEditorModeSettings.Modeling Mode=True
ModelingComponentsSettings.Modeling Tools=True
CustomizableObjectEditorSettings.Compilation=True
CustomizableObjectEditorSettings.AutomaticCompilation=True
MutableValidationSettings.Validation=True
NiagaraSettings.Niagara=True
NiagaraSettings.Viewport=True
NiagaraSettings.SimulationCaching=True
NiagaraSettings.Scalability=True
NiagaraSettings.Renderer=True
NiagaraSettings.LightRenderer=True
NiagaraSettings.SkeletalMeshDI=True
NiagaraSettings.StaticMeshDI=True
NiagaraSettings.AsyncGpuTraceDI=True
NiagaraSettings.SimCache=True
NiagaraEditorSettings.Niagara=True
NiagaraEditorSettings.SimulationOptions=True
NiagaraEditorSettings.Niagara Colors=True
NNEDenoiserSettings.NNE Denoiser=True
NNERuntimeORTSettings.ONNX Runtime=True
OpenColorIOSettings.Transform=True
PaperRuntimeSettings.Experimental=True
PaperRuntimeSettings.Settings=True
PythonScriptPluginSettings.Python=True
PythonScriptPluginSettings.PythonPipInstall=True
PythonScriptPluginSettings.PythonRemoteExecution=True
RenderDocPluginSettings.Frame Capture Settings=True
RenderDocPluginSettings.Advanced Settings=True
ResonanceAudioSettings.Reverb=True
ResonanceAudioSettings.General=True
TakeRecorderProjectSettings.Take Recorder=True
TakeRecorderProjectSettings.Movie Scene Take Settings=True
TakeRecorderProjectSettings.Microphone Audio Recorder=True
TakeRecorderProjectSettings.Audio Input Device=True
TakeRecorderProjectSettings.Animation Recorder=True
TakeRecorderProjectSettings.World Recorder=True
TcpMessagingSettings.Transport=True
UdpMessagingSettings.Availability=True
UdpMessagingSettings.Transport=True
UdpMessagingSettings.Tunnel=True
WmfMediaSettings.Media=True
WmfMediaSettings.Debug=True
PersonaPreviewSceneDescription.Mesh=True
PersonaPreviewSceneDescription.Physics=True
PersonaPreviewSceneDescription.Additional Meshes=True
PhysicsAssetGenerationSettings.Body Creation=True
PhysicsAssetGenerationSettings.Constraint Creation=True
Face_AnimBP_C.Default=True
Face_AnimBP_C.RootMotion=True
Face_AnimBP_C.Notifies=True
Face_AnimBP_C.Montage=True
Face_ControlBoard_CtrlRig_C.Default=True
SkeletalMeshComponent.TransformCommon=True
SkeletalMeshComponent.Animation=True
SkeletalMeshComponent.Mesh=True
SkeletalMeshComponent.Physics=True
SkeletalMeshComponent.Collision=True
SkeletalMeshComponent.Lighting=True
SkeletalMeshComponent.Clothing=True
SkeletalMeshComponent.LeaderPoseComponent=True
SkeletalMeshComponent.AnimationRig=True
SkeletalMeshComponent.Deformer=True
SkeletalMeshComponent.SkinWeights=True
SkeletalMeshComponent.Rendering=True
SkeletalMeshComponent.HLOD=True
SkeletalMeshComponent.Navigation=True
SkeletalMeshComponent.VirtualTexture=True
SkeletalMeshComponent.Tags=True
SkeletalMeshComponent.Activation=True
SkeletalMeshComponent.Cooking=True
SkeletalMeshComponent.Materials=True
SkeletalMesh.NaniteSettings=True
AnimSequence.Animation=True
AnimSequence.Compression=True
AnimSequence.AdditiveSettings=True
AnimSequence.RootMotion=True
AnimSequence.File Path=True
AnimSequence.CustomAttributes=True
AnimSequence.Animation Model=True
AnimSequence.MetaData=True
AnimSequence.Thumbnail=True
BatchRetargetSettings.Source=True
BatchRetargetSettings.Target=True
BatchRetargetSettings.Retargeter=True
BatchExportOptions.File=True
ControlRigPoseAsset.Pose=True
BP_MH_Friend_C.Tick=True
BP_MH_Friend_C.Live Retarget=True
BP_MH_Friend_C.Materials=True
BP_MH_Friend_C.Events=True
SkeletalMeshComponent.Variable=True
SkeletalMeshComponent.Sockets=True
SkeletalMeshComponent.ComponentTick=True
SkeletalMeshComponent.ComponentReplication=True
SkeletalMeshComponent.Events=True
AnimDetailControlsProxyTransform.Attributes=True

[ContentBrowser]
ContentBrowserTab1.SelectedPaths=/Game/MetaHumans/Test
ContentBrowserTab1.PluginFilters=
ContentBrowserTab1.SourcesExpanded=True
ContentBrowserTab1.IsLocked=False
ContentBrowserTab1.FavoritesAreaExpanded=False
ContentBrowserTab1.PathAreaExpanded=True
ContentBrowserTab1.CollectionAreaExpanded=False
ContentBrowserTab1.FavoritesSearchAreaExpanded=False
ContentBrowserTab1.PathSearchAreaExpanded=False
ContentBrowserTab1.CollectionSearchAreaExpanded=False
ContentBrowserTab1.VerticalSplitter.FixedSlotSize0=150
ContentBrowserTab1.VerticalSplitter.SlotSize1=0.75
ContentBrowserTab1.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab1.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab1.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab1.Favorites.SelectedPaths=
FavoritePaths=
ContentBrowserTab1.SelectedCollections=
ContentBrowserTab1.ExpandedCollections=
ContentBrowserTab1.ThumbnailSize=2
ContentBrowserTab1.CurrentViewType=1
ContentBrowserTab1.ZoomScale=0
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/Test
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/Test2
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/MH_Friend
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/MH_Friend/Face
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/Common/Face
ContentBrowserTab1.JumpMRU=/All/Game
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/Common
ContentBrowserTab1.JumpMRU=/All/Game/MetaHumans/MH_Friend/Face/Materials/Baked
ContentBrowserTab1.JumpMRU=/All/Plugins/BlenderLink/
AssetPropertyPicker.ThumbnailSize=2
AssetPropertyPicker.CurrentViewType=0
AssetPropertyPicker.ZoomScale=0
AssetDialog.ThumbnailSize=2
AssetDialog.CurrentViewType=1
AssetDialog.ZoomScale=0
ContentBrowserDrawer.SelectedPaths=/Game/MetaHumans/Test
ContentBrowserDrawer.PluginFilters=
ContentBrowserDrawer.SourcesExpanded=True
ContentBrowserDrawer.IsLocked=False
ContentBrowserDrawer.FavoritesAreaExpanded=False
ContentBrowserDrawer.PathAreaExpanded=True
ContentBrowserDrawer.CollectionAreaExpanded=False
ContentBrowserDrawer.FavoritesSearchAreaExpanded=False
ContentBrowserDrawer.PathSearchAreaExpanded=False
ContentBrowserDrawer.CollectionSearchAreaExpanded=False
ContentBrowserDrawer.VerticalSplitter.FixedSlotSize0=150
ContentBrowserDrawer.VerticalSplitter.SlotSize1=0.75
ContentBrowserDrawer.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserDrawer.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserDrawer.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserDrawer.Favorites.SelectedPaths=
ContentBrowserDrawer.SelectedCollections=
ContentBrowserDrawer.ExpandedCollections=
ContentBrowserDrawer.ThumbnailSize=2
ContentBrowserDrawer.CurrentViewType=1
ContentBrowserDrawer.ZoomScale=0
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Test
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common/Common/PoseLibrary/Face/Expressions
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common/Common/PoseLibrary/Face/Visemes
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common/Common/PoseLibrary/Face
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common/Common/PoseLibrary
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common/Common
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common/Face
ContentBrowserDrawer.JumpMRU=/All/Game/MetaHumans/Common
ContentBrowserDrawer.JumpMRU=/All/Game
SequencerAssetPicker.ThumbnailSize=2
SequencerAssetPicker.CurrentViewType=0
SequencerAssetPicker.ZoomScale=0
SequencerControlRigTrackAssetPicker.ThumbnailSize=2
SequencerControlRigTrackAssetPicker.CurrentViewType=0
SequencerControlRigTrackAssetPicker.ZoomScale=0
SequenceBrowser.ThumbnailSize=2
SequenceBrowser.CurrentViewType=2
SequenceBrowser.ZoomScale=0
SequenceBrowser.HiddenColumns=Source Frame Rate
SequenceBrowser.HiddenColumns=Number of Frames
SequenceBrowser.HiddenColumns=Number of Keys
SequenceBrowser.HiddenColumns=AnimSyncMarkerList
SequenceBrowser.HiddenColumns=Compressed Size (KB)
SequenceBrowser.HiddenColumns=Target Frame Rate
SequenceBrowser.HiddenColumns=ImportFileFramerate
SequenceBrowser.HiddenColumns=ImportResampleFramerate
SequenceBrowser.HiddenColumns=AdditiveAnimType
SequenceBrowser.HiddenColumns=RetargetSource
SequenceBrowser.HiddenColumns=RetargetSourceAsset
SequenceBrowser.HiddenColumns=Interpolation
SequenceBrowser.HiddenColumns=bEnableRootMotion
SequenceBrowser.HiddenColumns=bUseNormalizedRootMotionScale
SequenceBrowser.HiddenColumns=PlatformTargetFrameRate
SequenceBrowser.HiddenColumns=NumberOfSampledKeys
SequenceBrowser.HiddenColumns=SequenceLength
SequenceBrowser.HiddenColumns=Skeleton
SequenceBrowser.HiddenColumns=ParentAsset
SequenceBrowser.HiddenColumns=PreviewSkeletalMesh
SequenceBrowser.HiddenColumns=HasParentAsset
SequenceBrowser.HiddenColumns=AnimNotifyList
SequenceBrowser.HiddenColumns=CurveNameList
SequenceBrowser.HiddenColumns=Class
ControlPoseDialog.ThumbnailSize=2
ControlPoseDialog.CurrentViewType=1
ControlPoseDialog.ZoomScale=0

[RootWindow]
ScreenPosition=X=1280.000 Y=672.000
WindowSize=X=2554.000 Y=1434.000
InitiallyMaximized=False

[SlateAdditionalLayoutConfig]
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[Directories2]
UNR=H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test
BRUSH=H:/Plugins/BlenderLinkProject/Content/
FBX=H:/Plugins/BlenderLinkProject/Content/
FBXAnim=H:/Plugins/BlenderLinkProject/Content/
GenericImport=C:/Users/<USER>/Desktop/Metahuman.fbx
GenericExport=H:/Plugins/BlenderLinkProject/Content/
GenericOpen=H:/Plugins/BlenderLinkProject/Content/
GenericSave=H:/Plugins/BlenderLinkProject/Content/
MeshImportExport=H:/Plugins/BlenderLinkProject/Content/
WorldRoot=H:/Plugins/BlenderLinkProject/Content/
Level=H:/Plugins/BlenderLinkProject/Content/
Project=D:/UE_5.5/

[Python]
LastDirectory=
RecentsFiles=D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py

[PlacementMode]
RecentlyPlaced=/Game/MetaHumans/Test/Test.Test;
RecentlyPlaced=/Game/MetaHumans/Test/Metahuman.Metahuman;
RecentlyPlaced=/Game/MetaHumans/MH_Friend/BP_MH_Friend.BP_MH_Friend;

[DetailPropertyExpansion]
DNAAssetImportUI="\"Object.Mesh\" "
Object=
PreviewMeshCollection="\"Object.SkeletalMeshes\" "
DataAsset="\"Object.SkeletalMeshes\" "
MovieSceneBindingPropertyInfoList="\"Object.Binding Properties.Bindings.Bindings[0]\" "
MassSettings="\"Object.Mass\" "
DeveloperSettings=
AssetManagerSettings=
ConsoleSettings=
EnhancedInputDeveloperSettings=
DeveloperSettingsBackedByCVars=
InputSettings=
PaperImporterSettings=
DataflowSettings=
ModelingComponentsSettings=
SkeletalMesh="\"Object.SkeletalMesh.AssetUserData\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[1]\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[1].Object.ImportSettings\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2]\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General.Plugins\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General.ProjectSettings\" "
SkinnedAsset="\"Object.SkeletalMesh.AssetUserData\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[1]\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[1].Object.ImportSettings\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2]\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General.Plugins\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General.ProjectSettings\" "
StreamableRenderAsset="\"Object.SkeletalMesh.AssetUserData\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[1]\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[1].Object.ImportSettings\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2]\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General.Plugins\" \"Object.SkeletalMesh.AssetUserData.AssetUserData[2].Object.General.ProjectSettings\" "
LODInfoUILayout="\"Object.LODInfo.BuildSettings\" "
ControlRigPoseAsset="\"Object.Pose.Pose\" \"Object.Pose.Pose.CopyOfControls\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[0]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[0].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[0].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[0].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[0].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[1]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[1].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[1].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[1].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[1].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[10]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[10].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[10].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[10].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[10].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[100]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[100].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[100].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[100].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[100].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[101]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[101].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[101].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[101].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[101].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[102]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[102].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[102].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[102].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[102].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[103]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[103].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[103].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[103].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[103].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[104]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[104].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[104].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[104].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[104].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[105]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[105].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[105].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[105].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[105].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[106]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[106].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[106].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[106].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[106].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[107]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[107].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[107].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[107].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[107].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[108]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[108].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[108].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[108].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[108].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[109]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[109].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[109].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[109].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[109].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[11]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[11].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[11].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[11].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[11].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[110]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[110].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[110].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[110].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[110].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[111]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[111].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[111].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[111].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[111].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[112]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[112].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[112].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[112].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[112].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[113]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[113].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[113].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[113].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[113].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[114]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[114].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[114].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[114].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[114].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[115]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[115].ParentKey\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[115].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[115].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[115].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[115].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[116]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[116].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[116].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[116].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[116].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[117]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[117].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[117].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[117].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[117].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[118]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[118].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[118].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[118].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[118].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[119]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[119].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[119].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[119].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[119].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[12]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[12].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[12].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[12].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[12].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[120]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[120].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[120].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[120].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[120].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[121]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[121].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[121].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[121].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[121].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[122]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[122].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[122].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[122].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[122].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[123]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[123].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[123].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[123].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[123].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[124]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[124].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[124].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[124].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[124].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[125]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[125].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[125].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[125].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[125].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[126]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[126].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[126].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[126].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[126].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[127]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[127].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[127].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[127].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[127].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[128]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[128].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[128].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[128].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[128].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[129]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[129].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[129].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[129].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[129].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[13]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[13].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[13].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[13].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[13].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[130]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[130].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[130].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[130].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[130].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[131]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[131].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[131].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[131].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[131].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[132]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[132].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[132].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[132].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[132].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[133]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[133].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[133].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[133].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[133].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[134].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[134].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[134].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[134].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[135]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[135].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[135].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[135].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[135].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[136]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[136].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[136].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[136].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[136].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[137]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[137].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[137].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[137].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[137].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[138]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[138].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[138].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[138].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[138].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[139].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[139].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[139].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[139].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[14]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[14].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[14].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[14].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[14].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[140]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[140].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[140].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[140].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[140].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[141]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[141].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[141].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[141].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[141].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[142]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[142].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[142].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[142].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[142].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[143]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[143].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[143].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[143].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[143].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[144]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[144].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[144].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[144].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[144].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[145]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[145].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[145].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[145].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[145].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[146]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[146].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[146].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[146].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[146].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[147]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[147].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[147].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[147].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[147].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[148]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[148].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[148].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[148].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[148].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[149]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[149].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[149].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[149].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[149].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[15]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[15].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[15].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[15].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[15].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[150]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[150].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[150].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[150].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[150].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[151]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[151].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[151].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[151].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[151].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[152]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[152].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[152].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[152].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[152].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[153]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[153].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[153].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[153].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[153].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[154]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[154].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[154].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[154].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[154].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[155]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[155].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[155].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[155].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[155].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[156]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[156].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[156].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[156].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[156].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[157]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[157].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[157].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[157].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[157].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[158]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[158].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[158].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[158].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[158].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[159]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[159].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[159].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[159].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[159].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[16]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[16].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[16].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[16].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[16].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[160]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[160].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[160].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[160].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[160].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[161]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[161].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[161].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[161].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[161].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[162]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[162].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[162].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[162].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[162].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[163]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[163].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[163].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[163].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[163].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[164]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[164].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[164].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[164].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[164].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[165]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[165].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[165].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[165].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[165].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[17]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[17].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[17].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[17].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[17].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[18]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[18].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[18].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[18].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[18].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[19]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[19].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[19].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[19].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[19].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[2]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[2].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[2].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[2].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[2].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[20]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[20].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[20].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[20].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[20].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[21]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[21].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[21].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[21].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[21].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[22]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[22].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[22].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[22].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[22].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[23]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[23].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[23].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[23].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[23].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[24]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[24].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[24].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[24].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[24].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[25]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[25].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[25].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[25].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[25].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[26]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[26].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[26].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[26].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[26].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[27]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[27].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[27].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[27].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[27].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[28]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[28].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[28].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[28].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[28].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[29]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[29].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[29].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[29].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[29].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[3]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[3].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[3].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[3].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[3].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[30]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[30].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[30].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[30].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[30].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[31]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[31].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[31].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[31].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[31].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[32]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[32].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[32].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[32].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[32].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[33]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[33].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[33].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[33].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[33].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[34]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[34].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[34].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[34].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[34].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[35]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[35].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[35].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[35].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[35].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[36]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[36].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[36].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[36].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[36].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[37]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[37].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[37].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[37].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[37].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[38]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[38].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[38].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[38].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[38].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[39]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[39].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[39].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[39].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[39].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[4]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[4].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[4].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[4].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[4].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[40].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[40].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[40].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[40].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[41]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[41].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[41].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[41].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[41].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[42]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[42].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[42].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[42].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[42].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[43]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[43].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[43].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[43].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[43].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[44]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[44].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[44].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[44].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[44].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[45]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[45].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[45].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[45].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[45].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[46]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[46].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[46].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[46].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[46].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[47]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[47].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[47].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[47].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[47].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[48]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[48].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[48].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[48].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[48].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[49]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[49].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[49].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[49].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[49].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[5]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[5].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[5].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[5].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[5].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[50]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[50].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[50].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[50].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[50].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[51]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[51].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[51].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[51].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[51].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[52]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[52].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[52].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[52].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[52].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[53]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[53].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[53].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[53].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[53].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[54]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[54].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[54].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[54].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[54].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[55].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[55].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[55].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[55].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[56]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[56].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[56].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[56].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[56].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[57]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[57].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[57].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[57].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[57].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[58]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[58].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[58].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[58].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[58].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[59]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[59].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[59].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[59].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[59].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[6]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[6].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[6].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[6].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[6].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[60]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[60].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[60].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[60].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[60].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[61].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[61].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[61].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[61].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[62]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[62].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[62].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[62].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[62].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[63]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[63].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[63].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[63].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[63].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[64]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[64].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[64].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[64].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[64].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[65]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[65].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[65].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[65].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[65].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[66]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[66].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[66].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[66].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[66].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[67]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[67].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[67].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[67].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[67].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[68]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[68].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[68].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[68].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[68].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[69]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[69].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[69].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[69].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[69].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[7]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[7].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[7].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[7].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[7].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[70]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[70].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[70].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[70].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[70].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[71]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[71].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[71].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[71].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[71].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[72]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[72].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[72].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[72].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[72].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[73]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[73].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[73].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[73].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[73].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[74]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[74].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[74].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[74].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[74].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[75]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[75].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[75].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[75].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[75].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[76]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[76].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[76].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[76].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[76].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[77]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[77].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[77].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[77].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[77].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[78]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[78].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[78].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[78].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[78].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[79]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[79].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[79].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[79].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[79].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[8]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[8].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[8].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[8].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[8].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[80]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[80].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[80].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[80].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[80].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[81]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[81].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[81].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[81].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[81].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[82]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[82].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[82].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[82].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[82].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[83]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[83].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[83].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[83].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[83].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[84]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[84].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[84].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[84].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[84].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[85]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[85].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[85].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[85].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[85].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[86]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[86].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[86].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[86].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[86].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[87]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[87].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[87].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[87].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[87].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[88]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[88].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[88].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[88].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[88].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[89]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[89].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[89].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[89].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[89].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[9]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[9].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[9].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[9].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[9].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[90]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[90].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[90].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[90].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[90].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[91]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[91].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[91].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[91].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[91].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[92]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[92].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[92].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[92].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[92].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[93]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[93].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[93].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[93].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[93].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[94]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[94].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[94].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[94].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[94].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[95]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[95].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[95].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[95].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[95].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[96]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[96].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[96].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[96].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[96].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[97]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[97].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[97].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[97].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[97].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[98]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[98].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[98].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[98].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[98].GlobalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[99]\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[99].OffsetTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[99].ParentTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[99].LocalTransform\" \"Object.Pose.Pose.CopyOfControls.CopyOfControls[99].GlobalTransform\" "
InterchangeGenericAssetsPipeline="\"Object.Common.ImportOffsetRotation\" \"Object.Materials.MaterialPipeline.Object.Textures.TexturePipeline.Object.Textures.FileExtensionsToImportAsLongLatCubemap\" "
InterchangePipelineBase="\"Object.Common.ImportOffsetRotation\" \"Object.Materials.MaterialPipeline.Object.Textures.TexturePipeline.Object.Textures.FileExtensionsToImportAsLongLatCubemap\" "

[/Script/AudioEditor.AudioEditorSettings]
bUseAudioAttenuation=True
bPinSoundCueInAssetMenu=True
bPinSoundCueTemplateInAssetMenu=False
bPinSoundAttenuationInAssetMenu=True
bPinSoundConcurrencyInAssetMenu=True

[SkeletalMeshModelingTools]
EditingModeActive=True

[AssetEditorSubsystemRecents]
MRUItem0=/Game/MetaHumans/Test/Test
MRUItem1=/Game/MetaHumans/Test/Metahuman
MRUItem2=/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton
MRUItem3=/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh
MRUItem4=/Game/MetaHumans/MH_Friend/BP_MH_Friend
MRUItem5=/Game/MetaHumans/Common/Common/PoseLibrary/Face/Expressions/Anger-04_Rage
MRUItem6=/MetaHuman/IdentityTemplate/Face_Archetype_Skeleton
MRUItem7=/Game/MetaHumans/Test/Face_Archetype_Skeleton_Sequence
MRUItem8=/Game/MetaHumans/Common/Face/Face_ControlBoard_CtrlRig
MRUItem9=/Game/MetaHumans/Common/Face/Face_AnimBP
MRUItem10=/Game/MetaHumans/Test/MH_Friend_FaceMesh
MRUItem11=/Game/MetaHumans/Test/Metahuman_PhysicsAsset
MRUItem12=/Game/MetaHumans/MH_Friend/Face/Materials/Baked/MI_HeadSynthesized_Baked
MRUItem13=/Game/MetaHumans/MH_Friend/Face/FaceFoundationBlusherMask
MRUItem14=/Game/MetaHumans/MH_Friend/Face/FaceColor_MAIN
MRUItem15=/Game/MetaHumans/MH_Friend/Face/FaceColor_CM3

[AssetEditorToolkitTabLocation]
/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh=0
/Game/MetaHumans/MH_Friend/Face/FaceColor_CM3.FaceColor_CM3=1
/Game/MetaHumans/MH_Friend/Face/FaceColor_MAIN.FaceColor_MAIN=1
/Game/MetaHumans/MH_Friend/Face/FaceFoundationBlusherMask.FaceFoundationBlusherMask=1
/Game/MetaHumans/MH_Friend/Face/Materials/Baked/MI_HeadSynthesized_Baked.MI_HeadSynthesized_Baked=1
/Game/MetaHumans/Test/Metahuman.Metahuman=1
/MetaHuman/IdentityTemplate/Face_Archetype_Skeleton.Face_Archetype_Skeleton=1
/Game/MetaHumans/Common/Face/Face_AnimBP.Face_AnimBP=1
/Game/MetaHumans/Common/Face/Face_ControlBoard_CtrlRig.Face_ControlBoard_CtrlRig=1
/Game/MetaHumans/Test/Metahuman_PhysicsAsset.Metahuman_PhysicsAsset=1
/Game/MetaHumans/Test/MH_Friend_FaceMesh.MH_Friend_FaceMesh=1
/Game/MetaHumans/Test/Face_Archetype_Skeleton_Sequence.Face_Archetype_Skeleton_Sequence=1
/Game/MetaHumans/Common/Common/PoseLibrary/Face/Expressions/Anger-04_Rage.Anger-04_Rage=0
/Game/MetaHumans/MH_Friend/BP_MH_Friend.BP_MH_Friend=1
/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton=1

[/Script/PinnedCommandList.PinnedCommandListSettings]
Contexts=(Name="SkeletalMeshEditor.Viewport0",Commands=)
Contexts=(Name="SkeletalMeshEditor",Commands=)
Contexts=(Name="SkeletonEditor.Viewport0",Commands=)
Contexts=(Name="SkeletonEditor",Commands=)
Contexts=(Name="AnimationBlueprintEditor",Commands=)
Contexts=(Name="AnimationBlueprintEditor.Viewport0",Commands=)
Contexts=(Name="None0",Commands=)
Contexts=(Name="PhysicsAssetEditor",Commands=)
Contexts=(Name="PhysicsAssetEditor.Viewport0",Commands=)
Contexts=(Name="AnimationEditor.Viewport0",Commands=)
Contexts=(Name="AnimationEditor",Commands=)

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericAssetsPipeline]
PipelineDisplayName=Default Assets Pipeline
ReimportStrategy=ApplyNoProperties
bUseSourceNameForAsset=True
AssetName=
ImportOffsetTranslation=(X=0.000000,Y=0.000000,Z=0.000000)
ImportOffsetRotation=(Pitch=0.000000,Yaw=0.000000,Roll=0.000000)
ImportOffsetUniformScale=1.000000

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericCommonMeshesProperties]
ForceAllMeshAsType=IFMT_None
bAutoDetectMeshType=True
bImportLods=True
bBakeMeshes=True
bBakePivotMeshes=False
bKeepSectionsSeparate=False
VertexColorImportOption=IVCIO_Replace
VertexOverrideColor=(B=0,G=0,R=0,A=0)
bRecomputeNormals=True
bRecomputeTangents=True
bUseMikkTSpace=True
bComputeWeightedNormals=True
bUseHighPrecisionTangentBasis=False
bUseFullPrecisionUVs=False
bUseBackwardsCompatibleF16TruncUVs=False
bRemoveDegenerates=False

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericCommonSkeletalMeshesAndAnimationsProperties]
bImportOnlyAnimations=False
Skeleton=/Script/Engine.Skeleton'/Game/MetaHumans/Common/Face/Face_Archetype_Skeleton.Face_Archetype_Skeleton'
bImportMeshesInBoneHierarchy=True
bUseT0AsRefPose=False
bConvertStaticsWithMorphTargetsToSkeletals=False

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericMeshPipeline]
bImportStaticMeshes=True
bCombineStaticMeshes=False
LodGroup=None
bAutoComputeLODScreenSizes=True
bCollision=True
bImportCollisionAccordingToMeshName=True
bOneConvexHullPerUCX=True
Collision=Convex18DOP
bBuildNanite=True
bBuildReversedIndexBuffer=False
bGenerateLightmapUVs=False
bGenerateDistanceFieldAsIfTwoSided=False
bSupportFaceRemap=False
MinLightmapResolution=64
SrcLightmapIndex=0
DstLightmapIndex=1
BuildScale3D=(X=1.000000,Y=1.000000,Z=1.000000)
DistanceFieldResolutionScale=1.000000
DistanceFieldReplacementMesh=None
MaxLumenMeshCards=12
bImportSkeletalMeshes=True
SkeletalMeshImportContentType=All
bImportMorphTargets=True
bMergeMorphTargetsWithSameName=False
bImportVertexAttributes=False
bUpdateSkeletonReferencePose=False
bCreatePhysicsAsset=True
PhysicsAsset=None
bUseHighPrecisionSkinWeights=False
ThresholdPosition=0.000020
ThresholdTangentNormal=0.000020
ThresholdUV=0.000977
MorphThresholdPosition=0.015000
BoneInfluenceLimit=0

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericAnimationPipeline]
bImportAnimations=True
bImportBoneTracks=True
AnimationRange=Timeline
FrameImportRange=(Min=0,Max=0)
bUse30HzToBakeBoneAnimation=False
CustomBoneAnimationSampleRate=0
bSnapToClosestFrameBoundary=False
bImportCustomAttribute=True
bAddCurveMetadataToSkeleton=True
bSetMaterialDriveParameterOnCustomAttribute=False
MaterialCurveSuffixes=_mat
bRemoveCurveRedundantKeys=True
bDoNotImportCurveWithZero=True
bDeleteExistingNonCurveCustomAttributes=False
bDeleteExistingCustomAttributeCurves=False
bDeleteExistingMorphTargetCurves=True

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericMaterialPipeline]
PipelineDisplayName=
bImportMaterials=True
SearchLocation=Local
AssetName=
MaterialImport=ImportAsMaterials
bIdentifyDuplicateMaterials=False
bCreateMaterialInstanceForParent=False
ParentMaterial=None

[Interchange_StackName__Assets__PipelineClassName_InterchangeGenericTexturePipeline]
PipelineDisplayName=
bImportTextures=True
AssetName=
bDetectNormalMapTexture=True
bFlipNormalMapGreenChannel=False
bImportUDIMs=True
FileExtensionsToImportAsLongLatCubemap=("hdr")
bPreferCompressedSourceData=False
bAllowNonPowerOfTwo=True

[InterchangeSelectPipeline]
Assets_LastSelectedPipeline=InterchangeGenericAssetsPipeline

[InterchangeImportDialogOptions]
ImportContentDialogSizeX=1000
ImportContentDialogSizeY=650

[/Script/Engine.WorldPartitionEditorPerProjectUserSettings]
bHideEditorDataLayers=False
bHideRuntimeDataLayers=False
bHideDataLayerActors=True
bHideUnloadedActors=False
bShowOnlySelectedActors=False
bHighlightSelectedDataLayers=True
bHideLevelInstanceContent=True
bDisableLoadingOfLastLoadedRegions=False
bBugItGoLoadRegion=False
bShowCellCoords=False
MinimapUnloadedOpacity=0.660000
PerWorldEditorSettings=(("/Game/MetaHumans/Test/TestLevel.TestLevel", (LoadedEditorRegions=((Min=(X=-102480.867188,Y=-102536.195312,Z=-2097152.000000),Max=(X=102491.187500,Y=102276.851562,Z=2097152.000000),IsValid=True),(Min=(X=-102480.867188,Y=-102536.195312,Z=-2097152.000000),Max=(X=102491.187500,Y=102276.851562,Z=2097152.000000),IsValid=True)))))

[MaterialInstanceEditor]
bDrawGrid=False
PrimType=1

[/Script/UnrealEd.MaterialStatsOptions]
bPlatformUsed[0]=0
bPlatformUsed[1]=0
bPlatformUsed[2]=0
bPlatformUsed[3]=0
bPlatformUsed[4]=0
bPlatformUsed[5]=0
bPlatformUsed[6]=0
bPlatformUsed[7]=0
bPlatformUsed[8]=0
bPlatformUsed[9]=0
bPlatformUsed[10]=0
bPlatformUsed[11]=0
bPlatformUsed[12]=0
bPlatformUsed[13]=0
bPlatformUsed[14]=1
bPlatformUsed[15]=0
bPlatformUsed[16]=0
bPlatformUsed[17]=0
bPlatformUsed[18]=0
bPlatformUsed[19]=0
bPlatformUsed[20]=0
bPlatformUsed[21]=0
bPlatformUsed[22]=0
bPlatformUsed[23]=0
bPlatformUsed[24]=0
bPlatformUsed[25]=0
bPlatformUsed[26]=0
bPlatformUsed[27]=0
bPlatformUsed[28]=0
bPlatformUsed[29]=0
bPlatformUsed[30]=0
bPlatformUsed[31]=0
bPlatformUsed[32]=0
bPlatformUsed[33]=0
bPlatformUsed[34]=0
bPlatformUsed[35]=0
bPlatformUsed[36]=0
bPlatformUsed[37]=0
bPlatformUsed[38]=0
bPlatformUsed[39]=0
bPlatformUsed[40]=0
bPlatformUsed[41]=0
bPlatformUsed[42]=0
bPlatformUsed[43]=0
bPlatformUsed[44]=0
bPlatformUsed[45]=0
bPlatformUsed[46]=0
bPlatformUsed[47]=0
bPlatformUsed[48]=0
bPlatformUsed[49]=1
bPlatformUsed[50]=0
bPlatformUsed[51]=0
bPlatformUsed[52]=0
bPlatformUsed[53]=0
bPlatformUsed[54]=0
bPlatformUsed[55]=0
bPlatformUsed[56]=0
bPlatformUsed[57]=0
bPlatformUsed[58]=0
bPlatformUsed[59]=0
bPlatformUsed[60]=0
bPlatformUsed[61]=0
bPlatformUsed[62]=0
bPlatformUsed[63]=0
bPlatformUsed[64]=0
bPlatformUsed[65]=0
bPlatformUsed[66]=0
bPlatformUsed[67]=0
bPlatformUsed[68]=0
bPlatformUsed[69]=0
bPlatformUsed[70]=0
bPlatformUsed[71]=0
bPlatformUsed[72]=0
bPlatformUsed[73]=0
bPlatformUsed[74]=0
bPlatformUsed[75]=0
bPlatformUsed[76]=0
bPlatformUsed[77]=0
bPlatformUsed[78]=0
bPlatformUsed[79]=0
bPlatformUsed[80]=0
bPlatformUsed[81]=0
bPlatformUsed[82]=0
bPlatformUsed[83]=0
bPlatformUsed[84]=0
bPlatformUsed[85]=0
bPlatformUsed[86]=0
bPlatformUsed[87]=0
bPlatformUsed[88]=0
bPlatformUsed[89]=0
bPlatformUsed[90]=0
bPlatformUsed[91]=0
bPlatformUsed[92]=0
bPlatformUsed[93]=0
bPlatformUsed[94]=0
bPlatformUsed[95]=0
bPlatformUsed[96]=0
bPlatformUsed[97]=0
bPlatformUsed[98]=0
bPlatformUsed[99]=0
bPlatformUsed[100]=0
bPlatformUsed[101]=0
bPlatformUsed[102]=0
bPlatformUsed[103]=0
bPlatformUsed[104]=0
bPlatformUsed[105]=0
bPlatformUsed[106]=0
bPlatformUsed[107]=0
bPlatformUsed[108]=0
bPlatformUsed[109]=0
bPlatformUsed[110]=0
bPlatformUsed[111]=0
bPlatformUsed[112]=0
bPlatformUsed[113]=0
bPlatformUsed[114]=0
bPlatformUsed[115]=0
bPlatformUsed[116]=0
bPlatformUsed[117]=0
bPlatformUsed[118]=0
bPlatformUsed[119]=0
bPlatformUsed[120]=0
bPlatformUsed[121]=0
bPlatformUsed[122]=0
bPlatformUsed[123]=0
bPlatformUsed[124]=0
bPlatformUsed[125]=0
bPlatformUsed[126]=0
bPlatformUsed[127]=0
bPlatformUsed[128]=0
bPlatformUsed[129]=0
bPlatformUsed[130]=0
bPlatformUsed[131]=0
bPlatformUsed[132]=0
bPlatformUsed[133]=0
bPlatformUsed[134]=0
bPlatformUsed[135]=0
bPlatformUsed[136]=0
bPlatformUsed[137]=0
bPlatformUsed[138]=0
bPlatformUsed[139]=0
bPlatformUsed[140]=0
bPlatformUsed[141]=0
bPlatformUsed[142]=0
bPlatformUsed[143]=0
bPlatformUsed[144]=0
bPlatformUsed[145]=0
bPlatformUsed[146]=0
bPlatformUsed[147]=0
bPlatformUsed[148]=0
bPlatformUsed[149]=0
bPlatformUsed[150]=0
bPlatformUsed[151]=0
bPlatformUsed[152]=0
bPlatformUsed[153]=0
bPlatformUsed[154]=0
bMaterialQualityUsed[0]=0
bMaterialQualityUsed[1]=1
bMaterialQualityUsed[2]=0
bMaterialQualityUsed[3]=0
MaterialStatsDerivedMIOption=Ignore

[/Script/ControlRig.ControlRigPoseProjectSettings]
RootSaveDirs=(Path="ControlRig/Pose")
RootSaveDirs=(Path="/Game/MetaHumans/Common/Common/PoseLibrary/Face")

[DetailMultiObjectNodeExpansion]
GeneralProjectSettings=True
GameplayTagsSettings=True
GameMapsSettings=True
ProjectPackagingSettings=True
AssetManagerSettings=True
ConsoleSettings=True
EnhancedInputDeveloperSettings=True
EnhancedInputEditorProjectSettings=True
HierarchicalLODSettings=True
InputSettings=True
RecastNavMesh=True
PhysicsSettings=True
RendererSettings=True
WorldPartitionSettings=True
PaperImporterSettings=True
TextureImportSettings=True
AndroidRuntimeSettings=True
CameraCalibrationSettings=True
DataflowSettings=True
ModelingComponentsSettings=True

[/Script/UnrealEd.AnimationBlueprintEditorOptions]
bHideUnrelatedNodes=False

[/Script/BlueprintGraph.BlueprintEditorSettings]
bDrawMidpointArrowsInBlueprints=False
bShowGraphInstructionText=True
bHideUnrelatedNodes=False
bShowShortTooltips=True
bShowFunctionParameterIcon=True
bShowFunctionLocalVariableIcon=True
bEnableInputTriggerSupportWarnings=False
bSplitContextTargetSettings=True
bExposeAllMemberComponentFunctions=True
bShowContextualFavorites=False
bExposeDeprecatedFunctions=False
bCompactCallOnMemberNodes=False
bFlattenFavoritesMenus=True
bAutoCastObjectConnections=False
bShowViewportOnSimulate=False
bSpawnDefaultBlueprintNodes=True
bHideConstructionScriptComponentsInDetailsView=True
bHostFindInBlueprintsInGlobalTab=True
bNavigateToNativeFunctionsFromCallNodes=True
bDoubleClickNavigatesToParent=True
bEnableTypePromotion=True
TypePromotionPinDenyList=text
TypePromotionPinDenyList=string
BreakpointReloadMethod=RestoreAll
bEnablePinValueInspectionTooltips=True
bEnableNamespaceEditorFeatures=True
bEnableContextMenuTimeSlicing=True
ContextMenuTimeSlicingThresholdMs=50
bIncludeActionsForSelectedAssetsInContextMenu=False
bLimitAssetActionBindingToSingleSelectionOnly=False
bLoadSelectedAssetsForContextMenuActionBinding=True
bDoNotMarkAllInstancesDirtyOnDefaultValueChange=True
bFavorPureCastNodes=False
SaveOnCompile=SoC_Never
bJumpToNodeErrors=False
bAllowExplicitImpureNodeDisabling=False
bShowActionMenuItemSignatures=False
bBlueprintNodeUniqueNames=False
NodeTemplateCacheCapMB=20.000000
AllowIndexAllBlueprints=LoadOnly
bShowInheritedVariables=False
bAlwaysShowInterfacesInOverrides=True
bShowParentClassInOverrides=True
bShowEmptySections=True
bShowAccessSpecifier=False
Bookmarks=()
PerBlueprintSettings=()
bIncludeCommentNodesInBookmarksTab=True
bShowBookmarksForCurrentDocumentOnlyInTab=False
GraphEditorQuickJumps=()

[DetailCategoriesAdvanced]
Face_ControlBoard_CtrlRig_C.Default=False

