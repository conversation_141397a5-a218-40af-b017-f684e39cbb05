"""
Print Faceboard Positions Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to print the current faceboard bone positions
in a format suitable for the RigLogic API.
"""

import bpy
import json
import os
from pathlib import Path

from ..utils.dna_utils import get_gui_control_mapping_from_dna
from ..constants import FACE_BOARD_NAME

# Logging setup
try:
    # Try the direct import first (for development)
    from ..utils.logging_utils import get_logger
except ImportError:
    # Fall back to the full package path (for installed addon)
    from blender_metahuman_dna.blender_dna_plugin.utils.logging_utils import get_logger

log_info, log_debug, log_error = get_logger()
log_warning = lambda msg: log_info(f"WARNING: {msg}")

# Control mapping will be loaded from DNA file when needed
CONTROL_AXES = {}

class DNA_OT_PrintFaceboardPositions(bpy.types.Operator):
    """Print all faceboard bone positions in JSON format for RigLogic API"""
    bl_idname = "dna.print_faceboard_positions"
    bl_label = "Print Faceboard Positions"
    bl_description = "Print all faceboard bone positions in the format needed for RigLogic API"
    bl_options = {'REGISTER', 'UNDO'}

    def find_faceboard(self, context):
        """Find the faceboard in the scene"""
        dna_tools = context.scene.dna_tools
        faceboard_name = f"{dna_tools.dna_name}_{FACE_BOARD_NAME}"

        # Look for the faceboard by name
        faceboard = bpy.data.objects.get(faceboard_name)
        if faceboard and faceboard.type == 'ARMATURE':
            return faceboard

        # Fallback: look for any armature with 'face_gui' in the name
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and 'face_gui' in obj.name.lower():
                return obj

        return None

    def execute(self, context):
        faceboard = self.find_faceboard(context)

        if not faceboard:
            self.report({'ERROR'}, "No faceboard found in the scene. Create a faceboard first.")
            return {'CANCELLED'}

        # Get the DNA file path from scene properties
        dna_tools = context.scene.dna_tools
        if not dna_tools.dna_file_path:
            self.report({'ERROR'}, "No DNA file loaded. Load a DNA file first.")
            return {'CANCELLED'}

        # Load control mapping from DNA file (following example plugin approach)
        global CONTROL_AXES
        CONTROL_AXES = get_gui_control_mapping_from_dna(dna_tools.dna_file_path)

        if not CONTROL_AXES:
            log_warning("No GUI controls found in DNA file, using all axes for all controls")
            # Fallback: use all axes for all CTRL_ bones
            for pose_bone in faceboard.pose.bones:
                if pose_bone.name.startswith('CTRL_'):
                    CONTROL_AXES[pose_bone.name] = ['x', 'y', 'z']
        else:
            log_info(f"Loaded {len(CONTROL_AXES)} GUI controls from DNA file")

        # Collect bone positions
        bone_positions = []

        for pose_bone in faceboard.pose.bones:
            # Only process control bones (typically start with 'CTRL_')
            if pose_bone.name.startswith('CTRL_'):
                # Get the bone's location
                loc = pose_bone.location

                # Only process controls that exist in our mapping
                if pose_bone.name in CONTROL_AXES:
                    # Use the specific axes defined in the mapping
                    axes = CONTROL_AXES[pose_bone.name]
                    for axis in axes:
                        bone_positions.append({
                            "name": pose_bone.name,
                            "axis": axis,
                            "value": getattr(loc, axis)
                        })
                # Skip controls that aren't in the mapping - they're likely organizational controls
                # or controls that don't directly affect the DNA parameters

        # Print the bone positions in a format similar to the API JSON
        print("\n=== FACEBOARD BONE POSITIONS ===")
        print("{\n  \"controls\": [")

        for i, pos in enumerate(bone_positions):
            comma = "," if i < len(bone_positions) - 1 else ""
            print(f"    {{\"name\": \"{pos['name']}\", \"axis\": \"{pos['axis']}\", \"value\": {pos['value']}}}{comma}")

        print("  ]\n}")
        print("=== END FACEBOARD BONE POSITIONS ===\n")

        # Also print the total number of control values
        print(f"Total control values: {len(bone_positions)}")

        # Use the original CTRL_ prefix as DNA expects it
        print("\n=== FORMATTED FOR DNA API ===")
        print("{\n  \"controls\": [")

        dna_positions = []
        for i, pos in enumerate(bone_positions):
            # Keep the original CTRL_ prefix as DNA expects it
            dna_positions.append({
                "name": pos['name'],
                "axis": pos['axis'],
                "value": pos['value']
            })

        for i, pos in enumerate(dna_positions):
            comma = "," if i < len(dna_positions) - 1 else ""
            print(f"    {{\"name\": \"{pos['name']}\", \"axis\": \"{pos['axis']}\", \"value\": {pos['value']}}}{comma}")

        print("  ]\n}")
        print("=== END FORMATTED FOR DNA API ===\n")

        # Save to a JSON file in the same directory as the blend file
        if bpy.data.is_saved:
            blend_dir = os.path.dirname(bpy.data.filepath)
            json_path = os.path.join(blend_dir, "faceboard_positions.json")

            with open(json_path, 'w') as f:
                json.dump({"controls": dna_positions}, f, indent=2)

            print(f"Saved positions to: {json_path}")
            self.report({'INFO'}, f"Faceboard positions saved to {json_path}")
        else:
            self.report({'INFO'}, "Blend file not saved, JSON export skipped")

        return {'FINISHED'}

# Classes to register
classes = [
    DNA_OT_PrintFaceboardPositions,
]

def register():
    """Register the print faceboard positions operator"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the print faceboard positions operator"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
