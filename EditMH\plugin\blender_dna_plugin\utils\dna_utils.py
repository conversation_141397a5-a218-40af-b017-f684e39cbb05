"""
Utility functions for working with DNA files.
"""

import os
import sys
import bpy
import mathutils

# Variable to track DNA module availability
DNA_MODULES_AVAILABLE = False

def ensure_dna_modules_path():
    """Ensure the DNA modules are in the Python path"""
    print(f"Ensuring DNA modules path...")

    # Get the addon path
    addon_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    dnacalib_path = os.path.join(addon_path, "dnacalib", "py3.11")

    print(f"Addon path: {addon_path}")
    print(f"DNACalib path: {dnacalib_path}")

    # Add the path to sys.path if it's not already there
    if dnacalib_path not in sys.path:
        print(f"Adding {dnacalib_path} to sys.path")
        sys.path.append(dnacalib_path)
    else:
        print(f"{dnacalib_path} already in sys.path")

    return dnacalib_path

def check_dna_modules():
    """Check if the DNA modules are available"""
    global DNA_MODULES_AVAILABLE

    print(f"Checking DNA modules availability (cached: {DNA_MODULES_AVAILABLE})")

    if DNA_MODULES_AVAILABLE:
        print(f"DNA modules already available (cached)")
        return True

    # Get the addon path
    import os
    import sys
    import traceback

    # Try to find the DNA modules in the addon path
    addon_paths = []
    for path in sys.path:
        if "blender_metahuman_dna" in path and "dnacalib" in path:
            addon_paths.append(path)

    if addon_paths:
        print(f"Found potential DNA module paths: {addon_paths}")
    else:
        print(f"No DNA module paths found in sys.path")

    # Try to import the DNA modules
    try:
        print(f"Attempting to import dna module...")
        import dna
        print(f"dna module imported successfully from {dna.__file__}")

        print(f"Attempting to import dnacalib module...")
        import dnacalib
        print(f"dnacalib module imported successfully from {dnacalib.__file__}")

        # Test if we can access the required classes
        print(f"Testing DNA module functionality...")
        try:
            from dna import FileStream, BinaryStreamReader, Status, DataLayer_All
            print(f"DNA classes imported successfully")
            DNA_MODULES_AVAILABLE = True
            print(f"DNA modules are fully available")
            return True
        except (ImportError, AttributeError) as e:
            print(f"Error importing DNA classes: {str(e)}")
            traceback.print_exc()
            DNA_MODULES_AVAILABLE = False
            return False

    except ImportError as e:
        DNA_MODULES_AVAILABLE = False
        print(f"Error importing DNA modules: {str(e)}")
        traceback.print_exc()

        # Print the Python path for debugging
        print(f"Python path: {sys.path}")

        return False

def get_dna_version():
    """Get the version of the DNA modules"""
    if not check_dna_modules():
        return "DNA modules not available"

    try:
        # Import the modules here to ensure they're available
        import dna
        import dnacalib

        # This is a placeholder - the actual version retrieval will depend on the DNA API
        return "DNA modules available"
    except Exception as e:
        return f"Error getting DNA version: {e}"

def log_dna_info(message):
    """Log DNA information to the console"""
    print(f"[DNA Tools] {message}")

def get_dna_mesh_vertex_positions(reader, mesh_index):
    """Get the vertex positions for a mesh"""
    if not check_dna_modules():
        return []

    try:
        # Get the number of vertices
        vertex_count = reader.getVertexPositionCount(mesh_index)

        # Get the vertex positions
        positions = []
        for vertex_index in range(vertex_count):
            x = reader.getVertexPositionX(mesh_index, vertex_index)
            y = reader.getVertexPositionY(mesh_index, vertex_index)
            z = reader.getVertexPositionZ(mesh_index, vertex_index)
            positions.append(mathutils.Vector((x, y, z)))

        return positions
    except Exception as e:
        log_dna_info(f"Error getting vertex positions: {e}")
        return []

def get_dna_mesh_faces(reader, mesh_index):
    """Get the faces for a mesh"""
    if not check_dna_modules():
        return []

    try:
        # Get the number of faces
        face_count = reader.getFaceCount(mesh_index)

        # Get the faces
        faces = []
        for face_index in range(face_count):
            # Get the number of vertices in the face
            vertex_count = reader.getFaceVertexCount(mesh_index, face_index)

            # Get the vertex indices
            face = []
            for vertex_index in range(vertex_count):
                face.append(reader.getFaceVertexIndex(mesh_index, face_index, vertex_index))

            faces.append(face)

        return faces
    except Exception as e:
        log_dna_info(f"Error getting faces: {e}")
        return []

def get_dna_mesh_uvs(reader, mesh_index):
    """Get the UVs for a mesh"""
    if not check_dna_modules():
        return []

    try:
        # Get the number of UVs
        uv_count = reader.getVertexTextureCoordinateCount(mesh_index)

        # Get the UVs
        uvs = []
        for uv_index in range(uv_count):
            u = reader.getVertexTextureCoordinateU(mesh_index, uv_index)
            v = reader.getVertexTextureCoordinateV(mesh_index, uv_index)
            uvs.append((u, v))

        return uvs
    except Exception as e:
        log_dna_info(f"Error getting UVs: {e}")
        return []

def get_dna_mesh_normals(reader, mesh_index):
    """Get the normals for a mesh"""
    if not check_dna_modules():
        return []

    try:
        # Get the number of normals
        normal_count = reader.getVertexNormalCount(mesh_index)

        # Get the normals
        normals = []
        for normal_index in range(normal_count):
            x = reader.getVertexNormalX(mesh_index, normal_index)
            y = reader.getVertexNormalY(mesh_index, normal_index)
            z = reader.getVertexNormalZ(mesh_index, normal_index)
            normals.append(mathutils.Vector((x, y, z)))

        return normals
    except Exception as e:
        log_dna_info(f"Error getting normals: {e}")
        return []

def get_dna_joint_hierarchy(reader):
    """Get the joint hierarchy"""
    if not check_dna_modules():
        return []

    try:
        # Get the number of joints
        joint_count = reader.getJointCount()

        # Get the joint hierarchy
        joints = []
        for joint_index in range(joint_count):
            joint_name = reader.getJointName(joint_index)
            parent_index = reader.getJointParentIndex(joint_index)
            parent_name = reader.getJointName(parent_index) if parent_index < joint_count else "None"

            # Get the joint transform
            translation_x = reader.getJointTranslationX(joint_index)
            translation_y = reader.getJointTranslationY(joint_index)
            translation_z = reader.getJointTranslationZ(joint_index)

            rotation_x = reader.getJointRotationX(joint_index)
            rotation_y = reader.getJointRotationY(joint_index)
            rotation_z = reader.getJointRotationZ(joint_index)
            rotation_w = reader.getJointRotationW(joint_index)

            joints.append({
                'index': joint_index,
                'name': joint_name,
                'parent_index': parent_index,
                'parent_name': parent_name,
                'translation': mathutils.Vector((translation_x, translation_y, translation_z)),
                'rotation': mathutils.Quaternion((rotation_w, rotation_x, rotation_y, rotation_z))
            })

        return joints
    except Exception as e:
        log_dna_info(f"Error getting joint hierarchy: {e}")
        return []

def get_dna_blend_shapes(reader):
    """Get the blend shapes"""
    if not check_dna_modules():
        return []

    try:
        # Get the number of blend shapes
        blend_shape_count = reader.getBlendShapeChannelCount()

        # Get the blend shapes
        blend_shapes = []
        for bs_index in range(blend_shape_count):
            bs_name = reader.getBlendShapeChannelName(bs_index)
            blend_shapes.append({
                'index': bs_index,
                'name': bs_name
            })

        return blend_shapes
    except Exception as e:
        log_dna_info(f"Error getting blend shapes: {e}")
        return []

def get_gui_control_mapping_from_dna(dna_file_path):
    """Get GUI control names and axes from DNA file, following example plugin approach

    Args:
        dna_file_path: Path to the DNA file

    Returns:
        dict: Dictionary mapping control names to their axes
              Format: {"CTRL_controlname": ["x", "y", "z"]}
    """
    try:
        # Ensure DNA modules are available
        ensure_dna_modules_path()

        # Import DNA modules
        from dna import DataLayer_All, FileStream, Status, BinaryStreamReader

        # Open and read the DNA file
        stream = FileStream(dna_file_path, FileStream.AccessMode_Read, FileStream.OpenMode_Binary)
        reader = BinaryStreamReader(stream, DataLayer_All)
        reader.read()

        if not Status.isOk():
            raise Exception(f"Error reading DNA file: {Status.get().message}")

        # Get GUI control information from DNA file (following example plugin approach)
        control_mapping = {}

        for index in range(reader.getGUIControlCount()):
            full_name = reader.getGUIControlName(index)

            # Parse the control name and axis following example plugin logic
            # Format is typically "CTRL_controlname.translateX" or similar
            if '.' in full_name:
                control_name, axis_part = full_name.split('.', 1)

                # Extract the axis from the axis part (e.g., "translateX" -> "x")
                axis = axis_part.rsplit('t', -1)[-1].lower()

                # Initialize the control in the mapping if not present
                if control_name not in control_mapping:
                    control_mapping[control_name] = []

                # Add the axis if it's valid and not already present
                if axis in ['x', 'y', 'z'] and axis not in control_mapping[control_name]:
                    control_mapping[control_name].append(axis)

        log_dna_info(f"Loaded {len(control_mapping)} GUI controls from DNA file")

        return control_mapping

    except Exception as e:
        log_dna_info(f"Error reading GUI controls from DNA file: {str(e)}")
        return {}

# Classes to register
classes = []

def register():
    """Register the utility classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

    # Check DNA module availability at registration time
    if check_dna_modules():
        log_dna_info("DNA modules loaded successfully")
    else:
        log_dna_info("DNA modules not available")

def unregister():
    """Unregister the utility classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
