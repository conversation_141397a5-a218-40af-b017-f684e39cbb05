"""
Evaluate Faceboard Operator for the Blender MetaHuman DNA Plugin.

This module provides functionality to evaluate faceboard data using Unreal Engine's RigLogic.
"""

import bpy
import os
import json
import socket
import struct
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from ..utils.socket_utils import SocketClient
from ..utils.ui_utils import update_ui
from ..utils.dna_utils import get_gui_control_mapping_from_dna

# Logging setup
try:
    # Try the direct import first (for development)
    from ..utils.logging_utils import get_logger, log_info, log_error, log_warning, log_debug
except ImportError:
    # Fall back to the full package path (for installed addon)
    from blender_metahuman_dna.blender_dna_plugin.utils.logging_utils import get_logger, log_info, log_error, log_warning, log_debug

# Logger functions are imported directly

# Create reports directory if it doesn't exist
def ensure_reports_dir():
    """Ensure the reports directory exists."""
    reports_dir = Path(bpy.path.abspath("//")) / "reports"
    if not reports_dir.exists():
        reports_dir.mkdir(parents=True)
        log_info(f"Created reports directory: {reports_dir}")
    return reports_dir

# Control mapping will be loaded from DNA file when needed
CONTROL_AXES = {}

class DNA_OT_StopEvaluateFaceboard(bpy.types.Operator):
    """Stop the continuous evaluation of the faceboard"""
    bl_idname = "dna.stop_evaluate_faceboard"
    bl_label = "Stop Evaluation"
    bl_description = "Stop sending faceboard data to Unreal Engine"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Only enable this operator if faceboard evaluation is active"""
        return context.scene.dna_tools.is_faceboard_evaluation_active

    def execute(self, context):
        """Stop the faceboard evaluation"""
        # First, send a shutdown message to Unreal to notify it to close the connection
        if context.scene.dna_tools.faceboard_socket and context.scene.dna_tools.faceboard_socket.fileno() != -1:
            try:
                # Create a shutdown message
                shutdown_data = {
                    "command": "shutdown",
                    "message": "Blender is closing the connection"
                }

                # Convert to JSON
                json_data = json.dumps(shutdown_data)

                # Convert to binary format with header
                data_bytes = json_data.encode('utf-8')
                header = struct.pack('!I', len(data_bytes))
                binary_data = header + data_bytes

                # Send the shutdown message
                context.scene.dna_tools.faceboard_socket.sendall(binary_data)
                log_info("Sent shutdown message to Unreal")

                # Wait a short time for Unreal to process the message
                time.sleep(0.1)
            except Exception as e:
                log_error(f"Error sending shutdown message: {e}")

        # Set the evaluation state to inactive
        context.scene.dna_tools.is_faceboard_evaluation_active = False

        # Close the socket connection if it exists
        if context.scene.dna_tools.faceboard_socket:
            try:
                context.scene.dna_tools.faceboard_socket.close()
                log_info("Closed socket connection")
            except Exception as e:
                log_error(f"Error closing socket connection: {e}")

            # Clear the socket reference
            context.scene.dna_tools.faceboard_socket = None

        log_info("Faceboard evaluation stopped")
        self.report({'INFO'}, "Faceboard evaluation stopped")
        return {'FINISHED'}


class DNA_OT_EvaluateFaceboard(bpy.types.Operator):
    """Evaluate faceboard data using Unreal Engine's RigLogic via socket connection"""
    bl_idname = "dna.evaluate_faceboard"
    bl_label = "Evaluate Faceboard"
    bl_description = "Send faceboard data to Unreal Engine for RigLogic evaluation"
    bl_options = {'REGISTER', 'UNDO'}

    _timer = None
    _socket = None
    _connected = False
    _host = "127.0.0.1"
    _port = 2907

    # Track previous bone values to detect changes
    _previous_bone_values = {}

    # Threshold for detecting changes in bone values (to avoid sending tiny changes)
    _change_threshold = 0.0001

    # Flag to track if we're in an active transform
    _in_active_transform = False

    # Last time we sent data during an active transform
    _last_transform_send_time = 0

    # Response caching to avoid redundant calculations
    _response_cache = {}
    _cache_hits = 0
    _cache_misses = 0
    _max_cache_size = 100  # Maximum number of cached responses

    # Performance tracking
    _last_performance_log = 0
    _request_count = 0
    _total_request_time = 0

    # Adaptive timeout settings
    _min_timeout = 0.2  # Minimum timeout in seconds
    _max_timeout = 2.0  # Maximum timeout in seconds
    _current_timeout = 1.0  # Current timeout in seconds
    _timeout_adjust_rate = 0.1  # How quickly to adjust timeout

    @classmethod
    def poll(cls, context):
        """Only enable this operator if faceboard evaluation is not active"""
        return not context.scene.dna_tools.is_faceboard_evaluation_active

    def find_faceboard(self, context) -> Optional[bpy.types.Object]:
        """Find the faceboard in the scene.

        Args:
            context: Blender context

        Returns:
            The faceboard object, or None if not found
        """
        # Look for objects with 'face_gui' in the name
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and 'face_gui' in obj.name.lower():
                return obj
        return None

    def collect_faceboard_data(self, faceboard: bpy.types.Object, force_all=False) -> List[Dict[str, Any]]:
        """Collect control data from the faceboard.

        Args:
            faceboard: The faceboard object
            force_all: If True, return all control values regardless of changes

        Returns:
            List of control data dictionaries containing only changed values (or all if force_all=True)
        """
        controls = []
        changed_values = False
        current_bone_values = {}

        # Make sure we have the most up-to-date data, especially during transforms
        # This ensures we capture the current bone positions even during active dragging
        if faceboard.mode == 'POSE':
            # Force an update of the dependency graph to get the latest bone positions
            # This is especially important during active transforms
            bpy.context.view_layer.update()

        # Get the active bone if we're in pose mode
        active_bone = None
        if faceboard.mode == 'POSE' and bpy.context.active_pose_bone:
            active_bone = bpy.context.active_pose_bone
            # Note: We don't set _in_active_transform here anymore
            # It's now controlled by mouse events

        # First pass: collect all current values and detect changes
        # Process ALL GUI controls from DNA file (following example plugin approach)
        for control_name, axes in CONTROL_AXES.items():
            pose_bone = faceboard.pose.bones.get(control_name)
            if pose_bone:
                # Get the bone's location
                loc = pose_bone.location

                # Use the specific axes defined in the mapping
                for axis in axes:
                    # Create a unique key for this bone+axis
                    key = f"{control_name}:{axis}"
                    value = getattr(loc, axis)

                    # Store the current value
                    current_bone_values[key] = value

                    # Check if this is a new value or has changed significantly
                    if (key not in self._previous_bone_values or
                        abs(value - self._previous_bone_values[key]) > self._change_threshold):
                        changed_values = True

                        # If this is the active bone being transformed, log it for debugging
                        if active_bone and pose_bone.name == active_bone.name:
                            log_debug(f"Changed bone: {pose_bone.name}, Axis: {axis}, Value: {value}")

        # If no values have changed and we're not forcing all values, return an empty list
        if not changed_values and not force_all:
            return []

        # Second pass: build the controls list with only changed values (or all if force_all)
        # Process ALL GUI controls from DNA file (following example plugin approach)
        for control_name, axes in CONTROL_AXES.items():
            pose_bone = faceboard.pose.bones.get(control_name)
            if pose_bone:
                # Get the bone's location
                loc = pose_bone.location

                # Use the specific axes defined in the mapping
                for axis in axes:
                    # Create a unique key for this bone+axis
                    key = f"{control_name}:{axis}"
                    value = getattr(loc, axis)

                    # Only include changed values or all values if force_all is True
                    if (force_all or
                        key not in self._previous_bone_values or
                        abs(value - self._previous_bone_values[key]) > self._change_threshold):
                        controls.append({
                            "name": control_name,
                            "axis": axis,
                            "value": value
                        })

        # Update the previous values with the current values
        self._previous_bone_values.update(current_bone_values)

        # Log the number of changed controls
        if not force_all and changed_values:
            log_debug(f"Sending {len(controls)} changed controls out of {len(current_bone_values)} total")

        return controls

    def apply_rig_logic_results(self, context, results: Dict[str, Any]) -> None:
        """Apply the RigLogic evaluation results to the character.

        Args:
            context: Blender context
            results: The RigLogic evaluation results
        """
        if not results:
            log_error("No results to apply")
            return

        try:
            # Store current selection and mode to restore later
            current_active = context.view_layer.objects.active
            current_selected = [obj for obj in context.selected_objects]
            current_mode = context.object.mode if context.object else 'OBJECT'

            # Apply blendshape outputs
            if "blendshapes" in results:
                self.apply_blendshapes(context, results["blendshapes"])

            # Apply joint outputs
            if "joints" in results:
                self.apply_joints(context, results["joints"])

            # Apply animated map outputs
            if "animated_maps" in results:
                self.apply_animated_maps(context, results["animated_maps"])

            # Update the view layer to ensure changes are applied
            context.view_layer.update()

            # Force a dependency graph update to ensure the mesh updates are visible
            depsgraph = context.evaluated_depsgraph_get()
            depsgraph.update()

            # Force a redraw of all 3D views to ensure the mesh updates are visible
            for window in context.window_manager.windows:
                for area in window.screen.areas:
                    if area.type == 'VIEW_3D':
                        for region in area.regions:
                            if region.type == 'WINDOW':
                                region.tag_redraw()

            # Find the root armature to force an update
            root_armature = None
            for obj in bpy.data.objects:
                if obj.type == 'ARMATURE' and obj.name == 'root':
                    root_armature = obj
                    break

            if root_armature:
                # Switch to object mode
                if current_mode != 'OBJECT':
                    bpy.ops.object.mode_set(mode='OBJECT')

                # Select the root armature
                bpy.ops.object.select_all(action='DESELECT')
                root_armature.select_set(True)
                context.view_layer.objects.active = root_armature

                # Toggle pose mode to force an update
                bpy.ops.object.mode_set(mode='POSE')

                # Select a bone to force an update
                if len(root_armature.data.bones) > 0:
                    bpy.ops.pose.select_all(action='DESELECT')
                    root_armature.data.bones[0].select = True

                # Update the view layer again
                context.view_layer.update()
                depsgraph.update()

                # Toggle back to object mode
                bpy.ops.object.mode_set(mode='OBJECT')

            # Find a mesh to force an update
            mesh_obj = None
            for obj in bpy.data.objects:
                if obj.type == 'MESH' and obj.name.endswith('_lod0_mesh'):
                    mesh_obj = obj
                    break

            if mesh_obj:
                # Select the mesh
                bpy.ops.object.select_all(action='DESELECT')
                mesh_obj.select_set(True)
                context.view_layer.objects.active = mesh_obj

                # Toggle edit mode to force an update
                bpy.ops.object.mode_set(mode='EDIT')
                bpy.ops.object.mode_set(mode='OBJECT')

                # Update the view layer again
                context.view_layer.update()
                depsgraph.update()

            # Restore original selection and mode
            bpy.ops.object.select_all(action='DESELECT')
            for obj in current_selected:
                if obj:
                    obj.select_set(True)
            if current_active:
                context.view_layer.objects.active = current_active
                if current_mode != 'OBJECT':
                    bpy.ops.object.mode_set(mode=current_mode)

            # Final update and redraw
            context.view_layer.update()
            depsgraph = context.evaluated_depsgraph_get()
            depsgraph.update()

            # Force a redraw of all 3D views again
            for window in context.window_manager.windows:
                for area in window.screen.areas:
                    if area.type == 'VIEW_3D':
                        for region in area.regions:
                            if region.type == 'WINDOW':
                                region.tag_redraw()

            log_info("Applied RigLogic results to character")

        except Exception as e:
            log_error(f"Error applying RigLogic results: {e}")

    def apply_blendshapes(self, context, blendshapes: List[Dict[str, Any]]) -> None:
        """Apply blendshape values to the character.

        Args:
            context: Blender context
            blendshapes: List of blendshape dictionaries with name and value
        """
        # Import the mesh utilities
        from ..utils.mesh_utils import get_shape_key_container

        # Find all meshes created by the create model operator
        mesh_objects = []
        for obj in bpy.data.objects:
            if obj.type == 'MESH' and obj.data.shape_keys:
                mesh_objects.append(obj)
                log_info(f"Found mesh with shape keys: {obj.name}")

        if not mesh_objects:
            log_error("No meshes with shape keys found")
            return

        # Create a dictionary of shape key names to values for faster lookup
        shape_key_values = {}
        for bs in blendshapes:
            if isinstance(bs, dict) and "name" in bs and "value" in bs:
                # Convert string value to float if needed
                if isinstance(bs["value"], str):
                    try:
                        value = float(bs["value"])
                    except ValueError:
                        log_error(f"Invalid blendshape value for {bs['name']}: {bs['value']}")
                        continue
                else:
                    value = bs["value"]

                # Scale the value if needed (DNA uses cm, Blender uses m)
                # For blendshapes, we typically don't need to scale as they're relative values
                # But we'll keep this comment here in case scaling is needed in the future

                # Store the original shape key name for backward compatibility
                shape_key_values[bs["name"]] = value
            elif isinstance(bs, (int, float)):
                # Handle old format (list of values)
                log_warning("Received blendshapes in old format (list of values)")
                # Apply using old method
                for mesh_obj in mesh_objects:
                    shape_keys = mesh_obj.data.shape_keys.key_blocks
                    for i, value in enumerate(blendshapes):
                        if i + 1 < len(shape_keys):  # Skip the Basis shape key (index 0)
                            # Reset to 0 first (delta values)
                            shape_keys[i + 1].value = 0.0
                            # Apply new value
                            shape_keys[i + 1].value = value
                return

        # Apply shape key values to all meshes
        for mesh_obj in mesh_objects:
            # Get the shape key container for this mesh
            shape_key_container = get_shape_key_container(mesh_obj)
            if not shape_key_container:
                continue

            log_info(f"Applying blendshapes to mesh: {mesh_obj.name}")

            # Check if the shape key container name matches the mesh name
            # If not, update it to match the example plugin's approach
            if shape_key_container.name != mesh_obj.name:
                log_info(f"Updating shape key container name from {shape_key_container.name} to {mesh_obj.name}")
                shape_key_container.name = mesh_obj.name

            shape_keys = shape_key_container.key_blocks

            # First reset all shape keys to 0 (since we're applying delta values)
            for shape_key in shape_keys:
                # Skip the Basis shape key
                if shape_key.name != "Basis":
                    shape_key.value = 0.0

            # Apply the new values
            applied_count = 0
            for shape_key in shape_keys:
                # Skip the Basis shape key
                if shape_key.name == "Basis":
                    continue

                # Check for direct match first (backward compatibility)
                if shape_key.name in shape_key_values:
                    shape_key.value = shape_key_values[shape_key.name]
                    applied_count += 1
                else:
                    # Check for prefixed match (meshname__shapekey)
                    # Extract the shape key name without the mesh prefix
                    if "__" in shape_key.name:
                        _, unprefixed_name = shape_key.name.split("__", 1)
                        if unprefixed_name in shape_key_values:
                            shape_key.value = shape_key_values[unprefixed_name]
                            applied_count += 1

            # Make sure the mesh is updated
            # We don't use bpy.ops.object.select_all here to avoid context issues
            # Instead, we just update the view layer
            context.view_layer.update()

            # Force a dependency graph update to ensure the mesh updates are visible
            depsgraph = context.evaluated_depsgraph_get()
            depsgraph.update()

            log_info(f"Applied {applied_count} blendshape values to {mesh_obj.name}")

        log_info(f"Applied blendshapes to {len(mesh_objects)} meshes")

    def apply_joints(self, context, joints: List[Dict[str, Any]]) -> None:
        """Apply joint transformations to the character rig.

        Args:
            context: Blender context
            joints: List of joint transformation data
        """
        # Import the bone transformation utility
        from ..utils.rotation_utils import get_bone_rest_transformations
        from mathutils import Vector, Matrix, Euler, Quaternion
        import math

        # Find the character armature created by the create armature operator
        character_rig = None
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE' and 'face_gui' not in obj.name.lower():
                character_rig = obj
                log_info(f"Found armature: {obj.name}")
                break

        if not character_rig:
            log_error("No character rig found")
            return

        # Initialize rest pose data if not already stored
        if not hasattr(self, 'rest_pose') or not self.rest_pose:
            log_info(f"Initializing rest pose data for armature: {character_rig.name}")
            self.rest_pose = {}

            # Make sure all bones use the correct rotation mode
            for pose_bone in character_rig.pose.bones:
                if pose_bone.name.startswith("FACIAL_"):
                    pose_bone.rotation_mode = "XYZ"
                    # Save the rest pose and parent space matrix
                    try:
                        self.rest_pose[pose_bone.name] = get_bone_rest_transformations(pose_bone.bone)
                    except ValueError as e:
                        log_error(f"Error getting rest pose for bone {pose_bone.name}: {e}")
                        return

        # Apply joint transformations
        applied_count = 0
        for joint_data in joints:
            bone_name = joint_data["name"]
            if bone_name in character_rig.pose.bones and bone_name in self.rest_pose:
                pose_bone = character_rig.pose.bones[bone_name]

                # Get the rest pose values that we saved during initialization
                rest_location, rest_rotation, rest_scale, rest_to_parent_matrix = self.rest_pose[bone_name]

                # Handle the new transform array format
                if "transform" in joint_data:
                    transform = joint_data["transform"]
                    if len(transform) >= 10:  # Make sure we have all 10 values
                        # Convert string values to float if needed
                        transform_values = []
                        for val in transform:
                            if isinstance(val, str):
                                try:
                                    transform_values.append(float(val))
                                except ValueError:
                                    log_error(f"Invalid transform value for {bone_name}: {val}")
                                    continue
                            else:
                                transform_values.append(val)

                        # Extract the delta values
                        # Scale by 0.01 to convert from cm to m (DNA uses cm, Blender uses m)
                        location_delta = Vector((
                            transform_values[0] * 0.01,  # tx
                            transform_values[1] * 0.01,  # ty
                            transform_values[2] * 0.01   # tz
                        ))

                        # Convert quaternion to euler for delta calculation
                        # Note: The order in the transform array is [x, y, z, w]
                        quat = Quaternion((
                            transform_values[6],  # w
                            transform_values[3],  # x
                            transform_values[4],  # y
                            transform_values[5]   # z
                        ))
                        rotation_delta = quat.to_euler('XYZ')

                        # Scale delta
                        scale_delta = Vector((
                            transform_values[7],  # sx
                            transform_values[8],  # sy
                            transform_values[9]   # sz
                        ))

                        # Update the transformations using the rest pose and the delta values
                        # We need to copy the vectors so we don't modify the original rest pose
                        location = rest_location.copy() + location_delta
                        rotation = rest_rotation.copy()
                        rotation.x += rotation_delta.x
                        rotation.y += rotation_delta.y
                        rotation.z += rotation_delta.z
                        scale = rest_scale.copy()
                        scale.x += scale_delta.x
                        scale.y += scale_delta.y
                        scale.z += scale_delta.z

                        # Update the bone matrix using the matrix-based approach
                        modified_matrix = Matrix.LocRotScale(location, rotation, scale)
                        pose_bone.matrix_basis = rest_to_parent_matrix.inverted() @ modified_matrix

                        applied_count += 1

                # Handle the old format for backward compatibility
                else:
                    # Extract delta values from the old format
                    location_delta = Vector((0, 0, 0))
                    rotation_delta = Euler((0, 0, 0), 'XYZ')
                    scale_delta = Vector((0, 0, 0))

                    # Apply location
                    if "location" in joint_data:
                        # Scale by 0.01 to convert from cm to m
                        loc = joint_data["location"]
                        location_delta = Vector((loc[0] * 0.01, loc[1] * 0.01, loc[2] * 0.01))

                    # Apply rotation
                    if "rotation" in joint_data:
                        # Convert rotation values to radians if needed
                        rot = joint_data["rotation"]
                        rotation_delta = Euler(rot, 'XYZ')

                    # Apply scale
                    if "scale" in joint_data:
                        scale_delta = Vector(joint_data["scale"]) - Vector((1, 1, 1))

                    # Update the transformations using the rest pose and the delta values
                    location = rest_location.copy() + location_delta
                    rotation = rest_rotation.copy()
                    rotation.x += rotation_delta.x
                    rotation.y += rotation_delta.y
                    rotation.z += rotation_delta.z
                    scale = rest_scale.copy()
                    scale.x += scale_delta.x
                    scale.y += scale_delta.y
                    scale.z += scale_delta.z

                    # Update the bone matrix using the matrix-based approach
                    modified_matrix = Matrix.LocRotScale(location, rotation, scale)
                    pose_bone.matrix_basis = rest_to_parent_matrix.inverted() @ modified_matrix

                    applied_count += 1

        # Update the view layer to ensure the changes are applied
        context.view_layer.update()

        # Force a dependency graph update to ensure the mesh updates are visible
        depsgraph = context.evaluated_depsgraph_get()
        depsgraph.update()

        log_info(f"Applied {applied_count} joint transforms to armature: {character_rig.name}")

    def apply_animated_maps(self, context, animated_maps: List[Dict[str, Any]]) -> None:
        """Apply animated map values to the character materials.

        Args:
            context: Blender context
            animated_maps: List of animated map dictionaries with name and value
        """
        # Create a dictionary of animated map names to values for faster lookup
        animated_map_values = {}
        for am in animated_maps:
            if isinstance(am, dict) and "name" in am and "value" in am:
                # Convert string value to float if needed
                if isinstance(am["value"], str):
                    try:
                        value = float(am["value"])
                    except ValueError:
                        log_error(f"Invalid animated map value for {am['name']}: {am['value']}")
                        continue
                else:
                    value = am["value"]
                animated_map_values[am["name"]] = value
            elif isinstance(am, (int, float)):
                # Handle old format (list of values)
                log_warning("Received animated maps in old format (list of values)")
                break

        # Log the animated map values
        log_info(f"Received {len(animated_map_values)} animated map values")

        # TODO: Apply animated map values to materials
        # This would require identifying which materials use these animated maps
        # and setting the appropriate material parameters

        # For now, just log some of the values for debugging
        if animated_map_values:
            sample_values = list(animated_map_values.items())[:5]
            log_info(f"Sample animated map values: {sample_values}")

    def check_for_incoming_messages(self, context):
        """Check for any incoming messages from the server, even when not actively sending data"""
        if not self._socket or not self._connected:
            return False

        try:
            # Set a very short timeout for this check to avoid blocking
            original_timeout = self._socket.gettimeout()
            self._socket.settimeout(0.01)

            # Check if there's any data available
            try:
                # Try to receive the header (4 bytes)
                chunk = self._socket.recv(4)
                if chunk and len(chunk) == 4:
                    # We got a header, get the expected size
                    expected_size = struct.unpack('!I', chunk)[0]

                    # Reset timeout to normal for receiving the body
                    self._socket.settimeout(self._current_timeout)

                    # Try to receive the body
                    raw_data = chunk
                    while len(raw_data) - 4 < expected_size:
                        remaining = expected_size - (len(raw_data) - 4)
                        chunk = self._socket.recv(min(65536, remaining))
                        if not chunk:
                            break
                        raw_data += chunk

                    # If we have all the data, parse it
                    if len(raw_data) - 4 >= expected_size:
                        json_bytes = raw_data[4:4+expected_size]
                        json_str = json_bytes.decode('utf-8')
                        results = json.loads(json_str)

                        # Check if this is a command message (e.g., shutdown)
                        if "command" in results and results["command"] == "shutdown":
                            log_warning(f"Received shutdown command from Unreal: {results.get('message', 'No message provided')}")

                            # Reset the UI state to show the "Evaluate Faceboard" button instead of "Stop Evaluation"
                            context.scene.dna_tools.is_faceboard_evaluation_active = False
                            context.scene.dna_tools.faceboard_socket = None

                            # Close the socket properly
                            if self._socket:
                                try:
                                    self._socket.close()
                                except:
                                    pass
                                self._socket = None
                            self._connected = False

                            # Report to the user that the connection was closed by Unreal
                            self.report({'WARNING'}, f"Connection closed by Unreal: {results.get('message', 'No reason provided')}. Click 'Evaluate Faceboard' to reconnect.")

                            # Cancel the modal operator
                            if self._timer:
                                context.window_manager.event_timer_remove(self._timer)
                                self._timer = None

                            return True
                        else:
                            # If it's not a command, it's an unexpected message
                            log_warning(f"Received unexpected message from server: {results}")
                            return False
            except socket.timeout:
                # No data available, which is expected
                pass

        except Exception as e:
            # Only log errors occasionally to avoid console spam
            current_time = time.time()
            if not hasattr(self, '_last_check_error_time') or current_time - self._last_check_error_time > 5.0:
                log_error(f"Error checking for incoming messages: {e}")
                self._last_check_error_time = current_time

        finally:
            # Reset timeout to original value
            if self._socket and self._connected:
                try:
                    self._socket.settimeout(original_timeout)
                except:
                    pass

        return False

    def modal(self, context, event):
        """Handle modal events for continuous evaluation"""
        # Check for any incoming messages from the server
        if self._socket and self._connected:
            if self.check_for_incoming_messages(context):
                # If we received a shutdown message, the method will have already handled it
                return {'CANCELLED'}

        # Check if evaluation should be stopped
        if not context.scene.dna_tools.is_faceboard_evaluation_active:
            self.cancel(context)
            return {'CANCELLED'}

        # Get the evaluation mode
        evaluation_mode = context.scene.dna_tools.faceboard_evaluation_mode

        # Process timer events to send and receive data
        if event.type == 'TIMER':
            # Find the faceboard
            faceboard = self.find_faceboard(context)

            if not faceboard:
                self.report({'ERROR'}, "No faceboard found in the scene. Create a faceboard first.")
                self.cancel(context)
                return {'CANCELLED'}

            # Check if we're in pose mode and the faceboard is the active object
            is_active_transform = (context.mode == 'POSE' and
                                  context.active_object == faceboard and
                                  context.active_object.mode == 'POSE')

            # Handle different evaluation modes
            if evaluation_mode == 'CLICK':
                # In click mode, don't send data during active transform
                if self._in_active_transform:
                    # Skip sending data during active transform
                    log_debug("Skipping timer update during active transform (Click mode)")
                    return {'PASS_THROUGH'}
            elif evaluation_mode == 'CONTINUOUS':
                # In continuous mode, we'll send data even during active transform
                # but at a rate that adapts to the server's response time
                current_time = time.time()

                # Dynamically adjust the update frequency based on response time
                # If we're getting fast responses, we can send updates more frequently
                # If responses are slow, we'll reduce the frequency to avoid overloading
                update_interval = max(0.05, min(1.0, self._current_timeout * 0.5))

                # Only send updates at the adaptive rate
                if current_time - self._last_transform_send_time < update_interval:
                    return {'PASS_THROUGH'}

            # Collect faceboard data - but only when not in active transform
            # Collect only changed control values to reduce network traffic
            # We'll do a full sync every 30 seconds to ensure consistency
            current_time = time.time()
            force_all = False

            # Force a full sync periodically or if this is the first time
            if not hasattr(self, '_last_full_sync_time') or current_time - self._last_full_sync_time > 30.0:
                force_all = True
                self._last_full_sync_time = current_time
                log_debug("Performing full sync of all control values")

            # Collect only changed values (or all if force_all is True)
            controls = self.collect_faceboard_data(faceboard, force_all=force_all)

            # If no controls have changed and we're not forcing all, skip sending data
            if not controls:
                return {'PASS_THROUGH'}

            # Get DNA file path from scene properties
            dna_file = context.scene.dna_tools.dna_file_path

            if not dna_file:
                self.report({'ERROR'}, "No DNA file found in scene settings. Please import a DNA file first.")
                self.cancel(context)
                return {'CANCELLED'}

            # Log that we're sending data
            log_debug(f"Sending {len(controls)} controls to server")

            # Create the data packet
            data = {
                "dna_file": dna_file,
                "controls": controls
            }

            # Convert to JSON
            json_data = json.dumps(data)

            # Convert to binary format with header
            data_bytes = json_data.encode('utf-8')
            header = struct.pack('!I', len(data_bytes))
            binary_data = header + data_bytes

            # Track request time for performance monitoring
            request_start_time = time.time()
            request_success = False

            # Send the data
            try:
                if self._socket and self._connected:
                    self._socket.sendall(binary_data)

                    # Only log detailed debug info if we're not in an active transform
                    # to avoid flooding the console during dragging
                    if not is_active_transform:
                        log_debug(f"Sent {len(binary_data)} bytes to server")

                    # Receive the response
                    raw_data = b''

                    # Use adaptive timeout based on network performance
                    # During active transform, use a shorter timeout for responsiveness
                    if is_active_transform:
                        self._socket.settimeout(min(self._current_timeout, 0.5))  # Cap at 0.5s during transform
                    else:
                        self._socket.settimeout(self._current_timeout)

                    # First, try to receive the header (4 bytes)
                    header_received = False
                    expected_size = None

                    # Try to receive the header
                    try:
                        chunk = self._socket.recv(4)
                        if chunk:
                            raw_data = chunk
                            if len(raw_data) >= 4:
                                try:
                                    expected_size = struct.unpack('!I', raw_data[:4])[0]
                                    header_received = True

                                    # Now receive the body
                                    while len(raw_data) - 4 < expected_size:
                                        remaining = expected_size - (len(raw_data) - 4)
                                        chunk = self._socket.recv(min(65536, remaining))  # 64 KB buffer or remaining bytes
                                        if not chunk:
                                            break
                                        raw_data += chunk

                                    # If we have all the data, parse it
                                    if len(raw_data) - 4 >= expected_size:
                                        json_bytes = raw_data[4:4+expected_size]
                                        json_str = json_bytes.decode('utf-8')
                                        results = json.loads(json_str)

                                        # Check if this is a command message (e.g., shutdown)
                                        if "command" in results and results["command"] == "shutdown":
                                            log_warning(f"Received shutdown command from Unreal: {results.get('message', 'No message provided')}")

                                            # Reset the UI state to show the "Evaluate Faceboard" button instead of "Stop Evaluation"
                                            context.scene.dna_tools.is_faceboard_evaluation_active = False
                                            context.scene.dna_tools.faceboard_socket = None

                                            # Close the socket properly
                                            if self._socket:
                                                try:
                                                    self._socket.close()
                                                except:
                                                    pass
                                                self._socket = None
                                            self._connected = False

                                            # Report to the user that the connection was closed by Unreal
                                            self.report({'WARNING'}, f"Connection closed by Unreal: {results.get('message', 'No reason provided')}. Click 'Evaluate Faceboard' to reconnect.")

                                            # Cancel the modal operator
                                            if self._timer:
                                                context.window_manager.event_timer_remove(self._timer)
                                                self._timer = None

                                            # Return cancelled to stop the modal operator
                                            return {'CANCELLED'}

                                        # Cache the response for future use
                                        self.cache_response(controls, dna_file, results)

                                        # Save response JSON if enabled in debug settings
                                        if context.scene.dna_tools.save_response_json:
                                            self.save_response_json(results)

                                        # Save sent controls if enabled in debug settings
                                        if context.scene.dna_tools.save_sent_controls:
                                            self.save_sent_controls(controls, dna_file)

                                        # Apply the results to the character
                                        self.apply_rig_logic_results(context, results)

                                        # Update request tracking
                                        request_success = True
                                        request_time = time.time() - request_start_time
                                        self._request_count += 1
                                        self._total_request_time += request_time

                                        # Update adaptive timeout
                                        self.update_adaptive_timeout(True, request_time)

                                        # Reset consecutive timeouts counter since we got a successful response
                                        self._consecutive_timeouts = 0
                                except Exception as e:
                                    # Only log errors if we're not in an active transform
                                    if not is_active_transform:
                                        log_error(f"Error processing response: {e}")
                    except socket.timeout:
                        # Timeout is expected in real-time updates
                        # Update adaptive timeout for timeout case
                        self.update_adaptive_timeout(False)

                        # Check if we've had too many consecutive timeouts, which might indicate
                        # that the server has closed the connection due to inactivity
                        if not hasattr(self, '_consecutive_timeouts'):
                            self._consecutive_timeouts = 1
                        else:
                            self._consecutive_timeouts += 1

                        # If we've had too many consecutive timeouts (e.g., 10), assume the connection is dead
                        if self._consecutive_timeouts > 10:
                            log_warning("Too many consecutive timeouts. Assuming connection is dead.")

                            # Reset the UI state to show the "Evaluate Faceboard" button instead of "Stop Evaluation"
                            context.scene.dna_tools.is_faceboard_evaluation_active = False
                            context.scene.dna_tools.faceboard_socket = None

                            # Close the socket properly
                            if self._socket:
                                try:
                                    self._socket.close()
                                except:
                                    pass
                                self._socket = None
                            self._connected = False

                            # Report to the user that the connection was lost
                            self.report({'WARNING'}, "Connection to Unreal was lost due to inactivity. Click 'Evaluate Faceboard' to reconnect.")

                            # Cancel the modal operator
                            if self._timer:
                                context.window_manager.event_timer_remove(self._timer)
                                self._timer = None

                            # Return cancelled to stop the modal operator
                            return {'CANCELLED'}
                        pass
                    except Exception as e:
                        # Only log errors if we're not in an active transform
                        if not is_active_transform:
                            log_error(f"Error receiving data: {e}")
                        # Update adaptive timeout for error case
                        self.update_adaptive_timeout(False)
                else:
                    # Socket is not connected, try to reconnect
                    # But don't try to reconnect too frequently to avoid creating multiple connections
                    # Use a static variable to track the last reconnection attempt time
                    current_time = time.time()
                    if not hasattr(self, "_last_reconnect_time") or current_time - self._last_reconnect_time > 2.0:
                        log_info("Attempting to reconnect to server...")
                        self._last_reconnect_time = current_time
                        self.connect_to_server()
            except socket.error as e:
                # Handle socket errors specifically
                # Only log errors if we're not in an active transform
                if not is_active_transform:
                    log_error(f"Socket error: {e}")

                # Check if this is a connection reset or broken pipe error
                if isinstance(e, (ConnectionResetError, BrokenPipeError)) or "Connection reset" in str(e) or "Broken pipe" in str(e):
                    log_warning("Connection was reset by the server. Will reset UI and reconnect on next tick.")
                    # Close the socket properly
                    if self._socket:
                        try:
                            self._socket.close()
                        except:
                            pass
                        self._socket = None
                    self._connected = False

                    # Reset the UI state to show the "Evaluate Faceboard" button instead of "Stop Evaluation"
                    context.scene.dna_tools.is_faceboard_evaluation_active = False
                    context.scene.dna_tools.faceboard_socket = None

                    # Report to the user that the connection was lost
                    self.report({'WARNING'}, "Connection to Unreal was lost due to inactivity. Click 'Evaluate Faceboard' to reconnect.")

                    # Cancel the modal operator
                    if self._timer:
                        context.window_manager.event_timer_remove(self._timer)
                        self._timer = None

                    # Return cancelled to stop the modal operator
                    return {'CANCELLED'}

                # Don't try to reconnect immediately to avoid creating multiple connections
                # The next timer tick will handle reconnection
            except Exception as e:
                # Handle other exceptions
                # Only log errors if we're not in an active transform
                if not is_active_transform:
                    log_error(f"Error in socket communication: {e}")
                # Mark as disconnected, but don't try to reconnect immediately
                self._connected = False

        # Process mouse movement events - but don't send data during interactive movement
        elif event.type in {'MOUSEMOVE', 'INBETWEEN_MOUSEMOVE'} and context.active_object and context.active_object.mode == 'POSE':
            # Check if the active object is the faceboard
            faceboard = self.find_faceboard(context)
            if faceboard and context.active_object == faceboard:
                # We're actively moving a bone in the faceboard
                # But we won't send data until the transform is applied (mouse button released)
                # This reduces network traffic and only sends data when the user is done adjusting

                # Just mark that we're in an active transform
                self._in_active_transform = True

                # Track the current time for later use when the transform is applied
                self._last_transform_time = time.time()

                # Log this only occasionally to avoid console spam
                current_time = time.time()
                if not hasattr(self, '_last_transform_log_time') or current_time - self._last_transform_log_time > 5.0:
                    log_debug("Active bone transform in progress - will send data when transform is applied")
                    self._last_transform_log_time = current_time

            # Always pass through mouse movement events to ensure normal Blender behavior
            return {'PASS_THROUGH'}

        # Process mouse button release events to detect when a transform is completed
        elif event.type == 'LEFTMOUSE' and event.value == 'RELEASE' and context.active_object and context.active_object.mode == 'POSE':
            # Check if the active object is the faceboard and we were in an active transform
            faceboard = self.find_faceboard(context)
            if faceboard and context.active_object == faceboard and self._in_active_transform:
                # Transform has been applied (mouse button released)
                log_info("Transform applied - sending updated bone positions")

                # Reset the active transform flag
                self._in_active_transform = False

                # Collect all bone positions after the transform
                controls = self.collect_faceboard_data(faceboard, force_all=True)

                # Save sent controls if enabled in debug settings
                if context.scene.dna_tools.save_sent_controls:
                    # Get DNA file path from scene properties
                    dna_file = context.scene.dna_tools.dna_file_path
                    if dna_file:
                        self.save_sent_controls(controls, dna_file)

                # Only proceed if we have controls and a valid connection
                if controls and self._socket and self._connected:
                    # Get DNA file path from scene properties
                    dna_file = context.scene.dna_tools.dna_file_path

                    if dna_file:
                        # Create the data packet
                        data = {
                            "dna_file": dna_file,
                            "controls": controls
                        }

                        # Convert to JSON and send
                        try:
                            # Convert to binary format with header
                            json_data = json.dumps(data)
                            data_bytes = json_data.encode('utf-8')
                            header = struct.pack('!I', len(data_bytes))
                            binary_data = header + data_bytes

                            # Track request time for performance monitoring
                            request_start_time = time.time()

                            # Send the data
                            self._socket.sendall(binary_data)
                            log_info(f"Sent {len(binary_data)} bytes after transform completed")

                            # Use normal timeout for the response
                            self._socket.settimeout(self._current_timeout)

                            # Try to receive a response
                            try:
                                # Just check if there's a header (4 bytes)
                                chunk = self._socket.recv(4)
                                if chunk and len(chunk) == 4:
                                    # We got a header, get the expected size
                                    expected_size = struct.unpack('!I', chunk)[0]

                                    # Try to receive the body
                                    raw_data = chunk
                                    while len(raw_data) - 4 < expected_size:
                                        remaining = expected_size - (len(raw_data) - 4)
                                        chunk = self._socket.recv(min(65536, remaining))
                                        if not chunk:
                                            break
                                        raw_data += chunk

                                    # If we have all the data, parse and apply it
                                    if len(raw_data) - 4 >= expected_size:
                                        json_bytes = raw_data[4:4+expected_size]
                                        json_str = json_bytes.decode('utf-8')
                                        results = json.loads(json_str)

                                        # Check if this is a command message (e.g., shutdown)
                                        if "command" in results and results["command"] == "shutdown":
                                            log_warning(f"Received shutdown command from Unreal: {results.get('message', 'No message provided')}")

                                            # Reset the UI state to show the "Evaluate Faceboard" button instead of "Stop Evaluation"
                                            context.scene.dna_tools.is_faceboard_evaluation_active = False
                                            context.scene.dna_tools.faceboard_socket = None

                                            # Close the socket properly
                                            if self._socket:
                                                try:
                                                    self._socket.close()
                                                except:
                                                    pass
                                                self._socket = None
                                            self._connected = False

                                            # Report to the user that the connection was closed by Unreal
                                            self.report({'WARNING'}, f"Connection closed by Unreal: {results.get('message', 'No reason provided')}. Click 'Evaluate Faceboard' to reconnect.")

                                            # Cancel the modal operator
                                            if self._timer:
                                                context.window_manager.event_timer_remove(self._timer)
                                                self._timer = None

                                            # Return cancelled to stop the modal operator
                                            return {'CANCELLED'}

                                        # Cache the response for future use
                                        self.cache_response(controls, dna_file, results)

                                        # Save response JSON if enabled in debug settings
                                        if context.scene.dna_tools.save_response_json:
                                            self.save_response_json(results)

                                        # Apply the results to the character
                                        self.apply_rig_logic_results(context, results)

                                        # Update adaptive timeout with the response time
                                        request_time = time.time() - request_start_time
                                        self.update_adaptive_timeout(True, request_time)

                                        # Reset consecutive timeouts counter since we got a successful response
                                        self._consecutive_timeouts = 0

                                        log_info("Applied results after transform completed")
                            except socket.timeout:
                                log_warning("Timeout waiting for response after transform completed")
                                self.update_adaptive_timeout(False)
                            except Exception as e:
                                log_error(f"Error receiving data after transform: {e}")
                                self.update_adaptive_timeout(False)
                        except Exception as e:
                            log_error(f"Error sending data after transform: {e}")

            # Always pass through mouse button events
            return {'PASS_THROUGH'}

        # Continue running the modal operator
        return {'PASS_THROUGH'}

    def execute(self, context):
        """Start the continuous evaluation of the faceboard"""
        # Ensure reports directory exists
        ensure_reports_dir()

        # Get the DNA file path from scene properties and load control mapping
        dna_tools = context.scene.dna_tools
        if not dna_tools.dna_file_path:
            self.report({'ERROR'}, "No DNA file loaded. Load a DNA file first.")
            return {'CANCELLED'}

        # Load control mapping from DNA file (following example plugin approach)
        global CONTROL_AXES
        CONTROL_AXES = get_gui_control_mapping_from_dna(dna_tools.dna_file_path)

        if not CONTROL_AXES:
            log_warning("No GUI controls found in DNA file, using all axes for all controls")
            # Fallback: use all axes for all bones that could be controls
            faceboard = self.find_faceboard(context)
            if faceboard:
                for pose_bone in faceboard.pose.bones:
                    # Include any bone that could be a control (not just CTRL_ prefix)
                    # This matches the example plugin's approach of checking all bones
                    CONTROL_AXES[pose_bone.name] = ['x', 'y', 'z']
        else:
            log_info(f"Loaded {len(CONTROL_AXES)} GUI controls from DNA file")

        # Initialize reconnection timer
        self._last_reconnect_time = time.time()

        # Initialize transform tracking
        self._in_active_transform = False
        self._last_transform_send_time = time.time()
        self._last_transform_time = time.time()
        self._last_transform_log_time = time.time()
        self._last_full_sync_time = time.time()

        # Reset previous bone values
        self._previous_bone_values = {}

        # Reset response cache
        self._response_cache = {}
        self._cache_hits = 0
        self._cache_misses = 0

        # Reset performance tracking
        self._last_performance_log = time.time()
        self._request_count = 0
        self._total_request_time = 0

        # Reset adaptive timeout
        self._current_timeout = 1.0

        # Initialize consecutive timeouts counter
        self._consecutive_timeouts = 0

        # Set the evaluation state to active
        context.scene.dna_tools.is_faceboard_evaluation_active = True

        # Connect to the server
        self.connect_to_server()

        # Store the socket in the scene properties for access by the stop operator
        context.scene.dna_tools.faceboard_socket = self._socket

        # Start the timer for continuous evaluation
        # Use a slightly lower update rate to reduce socket communication frequency
        # This helps prevent connection issues while still providing smooth updates
        wm = context.window_manager
        self._timer = wm.event_timer_add(0.05, window=context.window)  # 20 FPS is still smooth but reduces network load

        # Register this operator as a modal operator
        wm.modal_handler_add(self)

        log_info("Started continuous faceboard evaluation")
        self.report({'INFO'}, "Started continuous faceboard evaluation")
        return {'RUNNING_MODAL'}

    def connect_to_server(self):
        """Connect to the Unreal Engine server"""
        # First, make sure any existing socket is properly closed
        if self._socket:
            try:
                self._socket.close()
                log_info("Closed existing socket before reconnecting")
            except Exception as e:
                log_error(f"Error closing existing socket: {e}")
            self._socket = None
            self._connected = False

        try:
            # Create a new socket
            self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._socket.settimeout(5.0)

            # Set socket options for better reliability
            self._socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)

            # Set socket option to reuse address to avoid "address already in use" errors
            self._socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # Set TCP keep-alive options to detect disconnections faster
            # These options may not be available on all platforms, so use try/except
            try:
                # TCP_KEEPIDLE: Time in seconds before sending keepalive probes
                self._socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, 10)

                # TCP_KEEPINTVL: Time in seconds between keepalive probes
                self._socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, 5)

                # TCP_KEEPCNT: Number of keepalive probes before dropping connection
                self._socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, 3)

                log_info("Set TCP keep-alive options for better connection reliability")
            except (AttributeError, OSError) as e:
                # These options might not be available on all platforms
                log_warning(f"Could not set TCP keep-alive options: {e}")

            # Increase the socket buffer size
            self._socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 524288)  # 512 KB receive buffer
            self._socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 524288)  # 512 KB send buffer

            # Connect to the server
            self._socket.connect((self._host, self._port))
            self._connected = True
            log_info(f"Connected to {self._host}:{self._port}")

            # Send an initial data message to Unreal with actual faceboard data
            try:
                # Find the faceboard
                faceboard = self.find_faceboard(bpy.context)
                if faceboard:
                    # Get DNA file path from scene properties
                    dna_file = bpy.context.scene.dna_tools.dna_file_path
                    if dna_file:
                        # Collect all bone positions
                        controls = self.collect_faceboard_data(faceboard, force_all=True)

                        # Create the data packet with both command and RigLogic data
                        data = {
                            "command": "connect",
                            "message": "Blender is establishing a connection",
                            "client_info": {
                                "client_type": "Blender",
                                "version": "1.0.0"
                            },
                            "dna_file": dna_file,
                            "controls": controls
                        }

                        # Convert to JSON
                        json_data = json.dumps(data)

                        # Convert to binary format with header
                        data_bytes = json_data.encode('utf-8')
                        header = struct.pack('!I', len(data_bytes))
                        binary_data = header + data_bytes

                        # Send the data
                        self._socket.sendall(binary_data)
                        log_info(f"Sent initial data message to Unreal with {len(controls)} controls")

                        # Save sent controls if enabled in debug settings
                        if bpy.context.scene.dna_tools.save_sent_controls:
                            self.save_sent_controls(controls, dna_file)
                    else:
                        log_warning("DNA file path not set, cannot send initial data")
                else:
                    log_warning("Faceboard not found, cannot send initial data")
            except Exception as e:
                log_error(f"Error sending initial data message: {e}")
        except Exception as e:
            log_error(f"Error connecting to server: {e}")
            if self._socket:
                try:
                    self._socket.close()
                except:
                    pass
                self._socket = None
            self._connected = False

    def get_cached_response(self, controls, dna_file):
        """Get a cached response for the given controls and DNA file.

        Args:
            controls: List of control dictionaries
            dna_file: Path to the DNA file

        Returns:
            Cached response if found, None otherwise
        """
        # Create a cache key from the controls and DNA file
        # We need a hashable key, so convert the controls to a tuple of tuples
        cache_key_parts = []
        for control in controls:
            # Only include the essential parts of the control
            cache_key_parts.append((
                control.get("name", ""),
                control.get("axis", ""),
                round(float(control.get("value", 0.0)), 4)  # Round to 4 decimal places
            ))

        # Sort to ensure consistent order
        cache_key_parts.sort()

        # Create the final cache key
        cache_key = (dna_file, tuple(cache_key_parts))

        # Check if we have a cached response
        if cache_key in self._response_cache:
            self._cache_hits += 1
            # Return a deep copy to avoid modifying the cached response
            return self._response_cache[cache_key].copy()

        self._cache_misses += 1
        return None

    def cache_response(self, controls, dna_file, response):
        """Cache a response for the given controls and DNA file.

        Args:
            controls: List of control dictionaries
            dna_file: Path to the DNA file
            response: Response to cache
        """
        # Create a cache key from the controls and DNA file
        cache_key_parts = []
        for control in controls:
            # Only include the essential parts of the control
            cache_key_parts.append((
                control.get("name", ""),
                control.get("axis", ""),
                round(float(control.get("value", 0.0)), 4)  # Round to 4 decimal places
            ))

        # Sort to ensure consistent order
        cache_key_parts.sort()

        # Create the final cache key
        cache_key = (dna_file, tuple(cache_key_parts))

        # Cache the response
        self._response_cache[cache_key] = response.copy()

        # Limit cache size
        if len(self._response_cache) > self._max_cache_size:
            # Remove the oldest entry (first key in the dictionary)
            oldest_key = next(iter(self._response_cache))
            del self._response_cache[oldest_key]

    def save_response_json(self, response):
        """Save the received JSON response to the reports folder.

        Args:
            response: The JSON response to save
        """
        try:
            # Ensure reports directory exists
            reports_dir = ensure_reports_dir()

            # Create a timestamp for the filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create the filename
            filename = f"riglogic_response_{timestamp}.json"
            filepath = reports_dir / filename

            # Save the JSON response
            with open(filepath, 'w') as f:
                json.dump(response, f, indent=2)

            log_info(f"Saved response JSON to {filepath}")
        except Exception as e:
            log_error(f"Error saving response JSON: {e}")

    def save_sent_controls(self, controls, dna_file):
        """Save the sent faceboard controls to the reports folder.

        Args:
            controls: The faceboard controls to save
            dna_file: The DNA file path
        """
        try:
            # Ensure reports directory exists
            reports_dir = ensure_reports_dir()

            # Create a timestamp for the filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Create the filename
            filename = f"faceboard_controls_{timestamp}.json"
            filepath = reports_dir / filename

            # Create the data packet
            data = {
                "dna_file": dna_file,
                "controls": controls,
                "timestamp": timestamp
            }

            # Save the JSON data
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            log_info(f"Saved sent controls to {filepath}")
        except Exception as e:
            log_error(f"Error saving sent controls: {e}")

    def update_adaptive_timeout(self, success, response_time=None):
        """Update the adaptive timeout based on network performance.

        Args:
            success: Whether the request was successful
            response_time: Time taken for the request in seconds
        """
        if success:
            # Successful request - gradually decrease timeout
            if response_time is not None:
                # Set timeout to slightly higher than the response time
                target_timeout = response_time * 1.5

                # But keep it within our min/max bounds
                target_timeout = max(self._min_timeout, min(self._max_timeout, target_timeout))

                # Gradually adjust toward the target
                self._current_timeout = (
                    (1 - self._timeout_adjust_rate) * self._current_timeout +
                    self._timeout_adjust_rate * target_timeout
                )
        else:
            # Failed request - increase timeout
            self._current_timeout = min(
                self._max_timeout,
                self._current_timeout * (1 + self._timeout_adjust_rate)
            )

        # Log the current timeout every 10 seconds
        current_time = time.time()
        if current_time - self._last_performance_log > 10.0:
            log_info(f"Current timeout: {self._current_timeout:.2f}s, " +
                     f"Cache hits: {self._cache_hits}, misses: {self._cache_misses}, " +
                     f"Hit rate: {self._cache_hits / max(1, self._cache_hits + self._cache_misses):.1%}")

            # Also log average request time if we have any
            if self._request_count > 0:
                avg_time = self._total_request_time / self._request_count
                log_info(f"Average request time: {avg_time:.3f}s over {self._request_count} requests")

            # Reset performance tracking
            self._last_performance_log = current_time

    def cancel(self, context):
        """Clean up when the operator is cancelled"""
        # Remove the timer
        if self._timer:
            context.window_manager.event_timer_remove(self._timer)
            self._timer = None

        # Send a shutdown message to Unreal before closing the socket
        if self._socket and self._connected:
            try:
                # Create a shutdown message
                shutdown_data = {
                    "command": "shutdown",
                    "message": "Blender is closing the connection"
                }

                # Convert to JSON
                json_data = json.dumps(shutdown_data)

                # Convert to binary format with header
                data_bytes = json_data.encode('utf-8')
                header = struct.pack('!I', len(data_bytes))
                binary_data = header + data_bytes

                # Send the shutdown message
                self._socket.sendall(binary_data)
                log_info("Sent shutdown message to Unreal")

                # Wait a short time for Unreal to process the message
                time.sleep(0.1)
            except Exception as e:
                log_error(f"Error sending shutdown message: {e}")

        # Close the socket
        if self._socket:
            try:
                self._socket.close()
                log_info("Closed socket connection")
            except Exception as e:
                log_error(f"Error closing socket connection: {e}")

            self._socket = None
            self._connected = False

        # Set the evaluation state to inactive
        context.scene.dna_tools.is_faceboard_evaluation_active = False
        context.scene.dna_tools.faceboard_socket = None

        # Log final cache statistics
        log_info(f"Final cache statistics - Hits: {self._cache_hits}, Misses: {self._cache_misses}, " +
                 f"Hit rate: {self._cache_hits / max(1, self._cache_hits + self._cache_misses):.1%}")

        log_info("Faceboard evaluation stopped")
        return {'CANCELLED'}



# Classes to register
classes = [
    DNA_OT_EvaluateFaceboard,
    DNA_OT_StopEvaluateFaceboard,
]

def register():
    """Register the evaluate faceboard operators"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister the evaluate faceboard operators"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
